{"version": 3, "file": "static/js/reactPlayerSoundCloud.bb986ce0.chunk.js", "mappings": "kFAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAqB,CAAC,EAzBXC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAoB,CAC3BK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,OAC/BC,EAAeD,EAAQ,MACvBE,EAAkBF,EAAQ,MAG9B,MAAMP,UAAmBG,EAAaO,UACpCC,WAAAA,GACEC,SAASC,WACTzB,EAAc0B,KAAM,aAAcN,EAAaO,YAC/C3B,EAAc0B,KAAM,WAAY,MAChC1B,EAAc0B,KAAM,cAAe,MACnC1B,EAAc0B,KAAM,iBAAkB,MACtC1B,EAAc0B,KAAM,QAAQ,KAC1BA,KAAKE,UAAU,EAAE,IAEnB5B,EAAc0B,KAAM,UAAU,KACF,OAAtBA,KAAKG,MAAMC,QACbJ,KAAKE,UAAUF,KAAKG,MAAMC,OAC5B,IAEF9B,EAAc0B,KAAM,OAAQK,IAC1BL,KAAKK,OAASA,CAAM,GAExB,CACAC,iBAAAA,GACEN,KAAKG,MAAMI,SAAWP,KAAKG,MAAMI,QAAQP,KAC3C,CACAQ,IAAAA,CAAKC,EAAKC,IACR,EAAIhB,EAAaiB,QAzBL,yCACG,MAwB+BC,MAAMC,IAClD,IAAKb,KAAKK,OACR,OACF,MAAM,KAAES,EAAI,cAAEC,EAAa,MAAEC,EAAK,OAAEC,EAAM,MAAEC,GAAUL,EAAGM,OAAOC,OAC3DV,IACHV,KAAKqB,OAASR,EAAGM,OAAOnB,KAAKK,QAC7BL,KAAKqB,OAAOC,KAAKR,EAAMd,KAAKG,MAAMoB,QAClCvB,KAAKqB,OAAOC,KAAKN,GAAO,KACJhB,KAAKwB,SAAWxB,KAAKyB,YACvB,KAGhBzB,KAAKG,MAAMuB,SAAS,IAEtB1B,KAAKqB,OAAOC,KAAKP,GAAgBY,IAC/B3B,KAAKyB,YAAcE,EAAEC,gBAAkB,IACvC5B,KAAK6B,eAAiBF,EAAEG,cAAc,IAExC9B,KAAKqB,OAAOC,KAAKL,GAAQ,IAAMjB,KAAKG,MAAM4B,YAC1C/B,KAAKqB,OAAOC,KAAKJ,GAAQS,GAAM3B,KAAKG,MAAM6B,QAAQL,MAEpD3B,KAAKqB,OAAOb,KAAKC,EAAK,IACjBT,KAAKG,MAAM8B,OAAOC,QACrBC,SAAUA,KACRnC,KAAKqB,OAAOe,aAAaZ,IACvBxB,KAAKwB,SAAWA,EAAW,IAC3BxB,KAAKG,MAAMkC,SAAS,GACpB,GAEJ,GAEN,CACAC,IAAAA,GACEtC,KAAKC,WAAW,OAClB,CACAsC,KAAAA,GACEvC,KAAKC,WAAW,QAClB,CACAuC,IAAAA,GACA,CACAC,MAAAA,CAAOC,GAA6B,IAApBC,IAAW5C,UAAA6C,OAAA,QAAAC,IAAA9C,UAAA,KAAAA,UAAA,GACzBC,KAAKC,WAAW,SAAoB,IAAVyC,GACrBC,GACH3C,KAAKuC,OAET,CACArC,SAAAA,CAAU4C,GACR9C,KAAKC,WAAW,YAAwB,IAAX6C,EAC/B,CACAV,WAAAA,GACE,OAAOpC,KAAKwB,QACd,CACAuB,cAAAA,GACE,OAAO/C,KAAKyB,WACd,CACAuB,gBAAAA,GACE,OAAOhD,KAAK6B,eAAiB7B,KAAKwB,QACpC,CACAyB,MAAAA,GACE,MAAM,QAAEC,GAAYlD,KAAKG,MACnBgD,EAAQ,CACZC,MAAO,OACPC,OAAQ,OACRH,WAEF,OAAuB7D,EAAaJ,QAAQqE,cAC1C,SACA,CACEC,IAAKvD,KAAKuD,IACVC,IAAK,wCAAwCC,mBAAmBzD,KAAKG,MAAMM,OAC3E0C,QACAO,YAAa,EACbC,MAAO,YAGb,EAEFrF,EAAcY,EAAY,cAAe,cACzCZ,EAAcY,EAAY,UAAWS,EAAgBiE,QAAQC,YAC7DvF,EAAcY,EAAY,eAAe,E", "sources": ["../node_modules/react-player/lib/players/SoundCloud.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar SoundCloud_exports = {};\n__export(SoundCloud_exports, {\n  default: () => SoundCloud\n});\nmodule.exports = __toCommonJS(SoundCloud_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://w.soundcloud.com/player/api.js\";\nconst SDK_GLOBAL = \"SC\";\nclass SoundCloud extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"fractionLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.setVolume(0);\n    });\n    __publicField(this, \"unmute\", () => {\n      if (this.props.volume !== null) {\n        this.setVolume(this.props.volume);\n      }\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((SC) => {\n      if (!this.iframe)\n        return;\n      const { PLAY, PLAY_PROGRESS, PAUSE, FINISH, ERROR } = SC.Widget.Events;\n      if (!isReady) {\n        this.player = SC.Widget(this.iframe);\n        this.player.bind(PLAY, this.props.onPlay);\n        this.player.bind(PAUSE, () => {\n          const remaining = this.duration - this.currentTime;\n          if (remaining < 0.05) {\n            return;\n          }\n          this.props.onPause();\n        });\n        this.player.bind(PLAY_PROGRESS, (e) => {\n          this.currentTime = e.currentPosition / 1e3;\n          this.fractionLoaded = e.loadedProgress;\n        });\n        this.player.bind(FINISH, () => this.props.onEnded());\n        this.player.bind(ERROR, (e) => this.props.onError(e));\n      }\n      this.player.load(url, {\n        ...this.props.config.options,\n        callback: () => {\n          this.player.getDuration((duration) => {\n            this.duration = duration / 1e3;\n            this.props.onReady();\n          });\n        }\n      });\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seekTo\", seconds * 1e3);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction * 100);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.fractionLoaded * this.duration;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        ref: this.ref,\n        src: `https://w.soundcloud.com/player/?url=${encodeURIComponent(this.props.url)}`,\n        style,\n        frameBorder: 0,\n        allow: \"autoplay\"\n      }\n    );\n  }\n}\n__publicField(SoundCloud, \"displayName\", \"SoundCloud\");\n__publicField(SoundCloud, \"canPlay\", import_patterns.canPlay.soundcloud);\n__publicField(SoundCloud, \"loopOnEnded\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "SoundCloud_exports", "__export", "target", "all", "name", "default", "SoundCloud", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "setVolume", "props", "volume", "iframe", "componentDidMount", "onMount", "load", "url", "isReady", "getSDK", "then", "SC", "PLAY", "PLAY_PROGRESS", "PAUSE", "FINISH", "ERROR", "Widget", "Events", "player", "bind", "onPlay", "duration", "currentTime", "onPause", "e", "currentPosition", "fractionLoaded", "loadedProgress", "onEnded", "onError", "config", "options", "callback", "getDuration", "onReady", "play", "pause", "stop", "seekTo", "seconds", "keepPlaying", "length", "undefined", "fraction", "getCurrentTime", "getSecondsLoaded", "render", "display", "style", "width", "height", "createElement", "ref", "src", "encodeURIComponent", "frameBorder", "allow", "canPlay", "soundcloud"], "sourceRoot": ""}