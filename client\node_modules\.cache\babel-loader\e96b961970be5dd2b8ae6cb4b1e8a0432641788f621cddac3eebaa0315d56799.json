{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{Container,Typography,Box,Paper,Grid,Card,CardContent,Button,Chip,CircularProgress,Alert,Dialog,DialogTitle,DialogContent,DialogActions,IconButton,Divider,Avatar,useTheme,Tooltip}from'@mui/material';import{CalendarMonth as CalendarIcon,AccessTime as TimeIcon,Person as PersonIcon,Cancel as CancelIcon,Close as CloseIcon,Event as EventIcon,VideoCall as VideoCallIcon,ChevronLeft as ChevronLeftIcon,ChevronRight as ChevronRightIcon}from'@mui/icons-material';import{format,addDays,startOfWeek,addWeeks,subWeeks}from'date-fns';import{ar,enUS}from'date-fns/locale';import axios from'../../utils/axios';import dayjs from'dayjs';import utc from'dayjs/plugin/utc';import{useAuth}from'../../contexts/AuthContext';import Layout from'../../components/Layout';import WeeklyBookingsTable from'../../components/WeeklyBookingsTable';import{convertFromDatabaseTime,formatDateInStudentTimezone,getCurrentTimeInTimezone}from'../../utils/timezone';import moment from'moment-timezone';import ProfileCompletionAlert from'../../components/student/ProfileCompletionAlert';import VideoSDKMeeting from'../../components/meeting/VideoSDKMeeting';import MeetingFeedbackDialog from'../../components/MeetingFeedbackDialog';import{toast}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";dayjs.extend(utc);const Bookings=()=>{const{t,i18n}=useTranslation();const{token}=useAuth();const theme=useTheme();const isRtl=i18n.language==='ar';const[bookings,setBookings]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[selectedBooking,setSelectedBooking]=useState(null);const[cancelDialogOpen,setCancelDialogOpen]=useState(false);const[detailsDialogOpen,setDetailsDialogOpen]=useState(false);const[cancellingBooking,setCancellingBooking]=useState(false);const[studentProfile,setStudentProfile]=useState(null);const[currentWeekStart,setCurrentWeekStart]=useState(()=>{const today=new Date();return startOfWeek(today,{weekStartsOn:1});// Start from current week\n});const[openMeeting,setOpenMeeting]=useState(false);const[currentMeeting,setCurrentMeeting]=useState(null);const[currentTime,setCurrentTime]=useState(new Date());const[feedbackDialogOpen,setFeedbackDialogOpen]=useState(false);const[feedbackMeeting,setFeedbackMeeting]=useState(null);// Days of the week\nconst daysOfWeek=['monday','tuesday','wednesday','thursday','friday','saturday','sunday'];// Week navigation functions\nconst goToPreviousWeek=()=>{const previousWeek=subWeeks(currentWeekStart,1);setCurrentWeekStart(previousWeek);};const goToNextWeek=()=>{const nextWeek=addWeeks(currentWeekStart,1);const today=new Date();const oneYearAhead=addWeeks(today,52);// One year ahead from today\nconst maxWeek=startOfWeek(oneYearAhead,{weekStartsOn:1});// Don't allow going beyond one year ahead\nif(nextWeek<=maxWeek){setCurrentWeekStart(nextWeek);}};// Check if navigation buttons should be disabled\nconst isPreviousWeekDisabled=()=>false;const isNextWeekDisabled=()=>{const nextWeek=addWeeks(currentWeekStart,1);const today=new Date();const oneYearAhead=addWeeks(today,52);// One year ahead from today\nconst maxWeek=startOfWeek(oneYearAhead,{weekStartsOn:1});return nextWeek>maxWeek;};// Fetch student profile\nuseEffect(()=>{const fetchStudentProfile=async()=>{if(!token)return;try{const{data}=await axios.get('/api/students/profile',{headers:{'Authorization':`Bearer ${token}`}});if(data.success&&data.profile){setStudentProfile(data.profile);}}catch(error){console.error('Error fetching student profile:',error);}};fetchStudentProfile();},[token]);// Check for pending feedback on mount\nuseEffect(()=>{const checkPending=async()=>{try{const{data}=await axios.get('/meeting-issues/pending');if(data.success&&data.data){const issue=data.data;const shouldOpen=dayjs.utc().isAfter(dayjs.utc(issue.datetime).add(issue.duration||50,'minute'));setFeedbackMeeting(issue);setFeedbackDialogOpen(shouldOpen);}}catch(err){console.error('Error checking pending feedback',err);}};checkPending();},[]);// Attach booking_id to feedback meeting if missing\nuseEffect(()=>{if(!feedbackMeeting||feedbackMeeting.booking_id)return;if(!bookings.length)return;const targetTime=moment(feedbackMeeting.datetime);// find booking with same teacher and date (same day) within 2 hours window\nconst candidate=bookings.find(b=>{if(b.teacher_name!==feedbackMeeting.teacher_name)return false;const bTime=moment(b.datetime);return Math.abs(bTime.diff(targetTime,'minutes'))<=120;});if(candidate){setFeedbackMeeting(prev=>({...prev,booking_id:candidate.id}));}},[bookings,feedbackMeeting]);// Fetch bookings with retry logic\nuseEffect(()=>{const fetchBookings=async()=>{try{setLoading(true);const{data}=await axios.get('/bookings/student',{headers:{'Authorization':`Bearer ${token}`}});if(data.success){console.log('Bookings data:',data.data);// Make sure all bookings have the correct data types\nconst processedBookings=data.data.map(booking=>({...booking,price_per_lesson:parseFloat(booking.price_per_lesson||0),price_paid:booking.price_paid!==undefined?parseFloat(booking.price_paid):null,duration:booking.duration?String(booking.duration):'50'}));console.log('Processed bookings:',processedBookings);setBookings(processedBookings);}else{setError(data.message||t('bookings.fetchError'));}}catch(error){console.error('Error fetching bookings:',error);setError(t('bookings.fetchError'));}finally{setLoading(false);}};const fetchWithRetry=async function(){let maxRetries=arguments.length>0&&arguments[0]!==undefined?arguments[0]:3;let retries=0;while(retries<maxRetries){try{await fetchBookings();break;}catch(error){retries++;if(retries===maxRetries){throw error;}await new Promise(resolve=>setTimeout(resolve,1000));// Wait before retry\n}}};if(token){fetchWithRetry();}// Update current time every second\nconst timeInterval=setInterval(()=>{setCurrentTime(new Date());},1000);return()=>clearInterval(timeInterval);},[token,t]);// Handle view details\nconst handleViewDetails=booking=>{setSelectedBooking(booking);setDetailsDialogOpen(true);};// Handle cancel booking\nconst handleCancelBookingClick=booking=>{setSelectedBooking(booking);setCancelDialogOpen(true);};// Handle join meeting\nconst handleJoinMeeting=async booking=>{try{// Check if room_name exists from the booking data\nif(!booking.room_name){console.error('No room_name found for booking:',booking);toast.error(t('meetings.noRoomError')||'Meeting room not found');return;}// Check if meeting_id exists\nif(!booking.meeting_id){console.error('No meeting_id found for booking:',booking);toast.error(t('meetings.noMeetingError')||'Meeting ID not found');return;}console.log('Joining meeting with data:',{room_name:booking.room_name,meeting_id:booking.meeting_id,datetime:booking.datetime,duration:booking.duration});// التحقق من صلاحية الغرفة\nconst response=await axios.get(`/meetings/${booking.room_name}/validate`);setCurrentMeeting({...booking,room_name:booking.room_name});setOpenMeeting(true);}catch(error){console.error('Error joining meeting:',error);toast.error(t('meetings.joinError'));}};const handleCloseMeeting=()=>{// After meeting dialog closes, prompt for feedback\nif(currentMeeting){const meetingEnd=new Date(currentMeeting.datetime);meetingEnd.setMinutes(meetingEnd.getMinutes()+(currentMeeting.duration||50));const now=new Date();// Only prompt if meeting time has actually ended\nif(now>=meetingEnd){setFeedbackMeeting(currentMeeting);// Send pending status immediately\naxios.post('/meeting-issues',{booking_id:currentMeeting.id,meeting_id:currentMeeting.meeting_id||currentMeeting.id,issue_type:'pending',description:''}).catch(err=>console.error('Failed to create pending issue',err));setFeedbackDialogOpen(true);}}setOpenMeeting(false);setCurrentMeeting(null);};// Get current time in student's timezone (same method as meetings page)\nconst getCurrentTimeInStudentTimezone=()=>{if(!studentProfile||!studentProfile.timezone){return new Date();}return getCurrentTimeInTimezone(studentProfile.timezone);};// Get meeting date in student timezone (same method as meetings page)\nconst getMeetingDateInStudentTimezone=datetime=>{if(!studentProfile||!studentProfile.timezone){return new Date(datetime);}return convertFromDatabaseTime(datetime,studentProfile.timezone);};// Get meeting status from database directly\nconst getMeetingStatus=booking=>{return booking.status||'scheduled';};// Check if user can join meeting (same method as meetings page)\nconst canJoinMeeting=booking=>{if(!booking||!studentProfile)return false;const currentStatus=getMeetingStatus(booking);if(currentStatus==='cancelled'||currentStatus==='completed'){return false;}const meetingStartTime=getMeetingDateInStudentTimezone(booking.datetime);const meetingEndTime=new Date(meetingStartTime);meetingEndTime.setMinutes(meetingEndTime.getMinutes()+(booking.duration||50));const now=getCurrentTimeInStudentTimezone();return now>=meetingStartTime&&now<meetingEndTime;};// Get meeting status text (same method as meetings page)\nconst getMeetingStatusText=booking=>{if(!booking||!studentProfile)return t('meetings.notStarted');const currentStatus=getMeetingStatus(booking);const canJoin=canJoinMeeting(booking);if(canJoin){return t('meetings.join');}switch(currentStatus){case'cancelled':return t('meetings.status.cancelled');case'completed':return t('meetings.status.completed');case'ongoing':return t('meetings.join');case'scheduled':default:return t('meetings.notStarted');}};// Handle booking cancellation\nconst handleCancelBooking=async()=>{if(!selectedBooking)return;try{setCancellingBooking(true);const{data}=await axios.put(`/bookings/${selectedBooking.id}/cancel`,{},{headers:{'Authorization':`Bearer ${token}`}});if(data.success){// Update the booking status in the local state\nsetBookings(prevBookings=>prevBookings.map(booking=>booking.id===selectedBooking.id?{...booking,status:'cancelled'}:booking));toast.success(t('bookings.cancelSuccess'));}else{toast.error(data.message||t('bookings.cancelError'));}}catch(error){var _error$response,_error$response$data;console.error('Error cancelling booking:',error);toast.error(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||t('bookings.cancelError'));}finally{setCancellingBooking(false);setCancelDialogOpen(false);setDetailsDialogOpen(false);setSelectedBooking(null);}};// Get status chip color\nconst getStatusColor=status=>{switch(status){case'scheduled':return'primary';case'completed':return'success';case'cancelled':return'error';case'issue_reported':return'warning';case'ongoing':return'info';default:return'default';}};// Get translated status text\nconst getStatusText=status=>{return t(`bookings.statusValues.${status}`,{defaultValue:status.charAt(0).toUpperCase()+status.slice(1)});};// Format booking date in student's timezone\nconst formatBookingDate=datetime=>{if(!studentProfile||!studentProfile.timezone){return format(new Date(datetime),'PPP',{locale:isRtl?ar:enUS});}const formattedDate=formatDateInStudentTimezone(datetime,studentProfile.timezone,'YYYY-MM-DD');return moment(formattedDate,'YYYY-MM-DD').format('MMMM D, YYYY');};// Format booking time in student's timezone\nconst formatBookingTime=datetime=>{if(!studentProfile||!studentProfile.timezone){return format(new Date(datetime),'p',{locale:isRtl?ar:enUS});}const formattedDateTime=formatDateInStudentTimezone(datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');return moment(formattedDateTime,'YYYY-MM-DD HH:mm:ss').format('h:mm A');};// Format booking time range (start - end) in student's timezone\nconst formatBookingTimeRange=(datetime,duration)=>{if(!studentProfile||!studentProfile.timezone){const startDate=new Date(datetime);const endDate=new Date(startDate.getTime()+duration*60000);const startTimeStr=startDate.toLocaleTimeString([],{hour:'2-digit',minute:'2-digit',hour12:false});const endTimeStr=endDate.toLocaleTimeString([],{hour:'2-digit',minute:'2-digit',hour12:false});return`${startTimeStr} - ${endTimeStr}`;}// Use student timezone for accurate time calculation\nconst formattedDateTime=formatDateInStudentTimezone(datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');const[datePart,timePart]=formattedDateTime.split(' ');const[hours,minutes]=timePart.split(':');// Calculate start and end times\nconst startMinutes=parseInt(hours)*60+parseInt(minutes);const endMinutes=startMinutes+duration;const startHour=Math.floor(startMinutes/60);const startMin=startMinutes%60;const endHour=Math.floor(endMinutes/60);const endMin=endMinutes%60;const startTimeStr=`${String(startHour).padStart(2,'0')}:${String(startMin).padStart(2,'0')}`;const endTimeStr=`${String(endHour).padStart(2,'0')}:${String(endMin).padStart(2,'0')}`;return`${startTimeStr} - ${endTimeStr}`;};// Submit feedback after meeting\nconst handleFeedbackSubmit=async(meetingId,values)=>{try{const response=await axios.post('/meeting-issues',{meeting_id:meetingId,booking_id:values.booking_id,issue_type:values.issue_type,description:values.description});// Update booking status locally for immediate UI feedback\nif(values.booking_id){const newStatus=values.issue_type==='no_issue'?'completed':'issue_reported';setBookings(prev=>prev.map(b=>b.id===values.booking_id?{...b,status:newStatus}:b));}}catch(err){console.error('Failed to submit feedback',err);}finally{setFeedbackDialogOpen(false);setFeedbackMeeting(null);}};// Render booking cards\nconst renderBookings=()=>{if(bookings.length===0){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:t('bookings.noBookings')})});}return/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,children:bookings.map(booking=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Card,{elevation:3,sx:{height:'100%',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(Box,{sx:{bgcolor:'primary.main',color:'white',p:2,display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(CalendarIcon,{}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:formatBookingDate(booking.datetime)})]}),/*#__PURE__*/_jsxs(CardContent,{sx:{flexGrow:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Avatar,{src:booking.teacher_picture,alt:booking.teacher_name,sx:{mr:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",children:booking.teacher_name})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(TimeIcon,{sx:{mr:1,color:'text.secondary'}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:formatBookingTime(booking.datetime)})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(TimeIcon,{sx:{mr:1,color:'text.secondary'}}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[t('bookings.duration'),\": \",booking.duration||50,\" \",t('bookings.minutes'),booking.duration==='25'||booking.duration===25?` (${t('booking.halfLesson')||'نصف درس'})`:` (${t('booking.fullLesson')||'درس كامل'})`]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(EventIcon,{sx:{mr:1,color:'text.secondary'}}),/*#__PURE__*/_jsx(Chip,{label:getStatusText(booking.status),color:getStatusColor(booking.status),size:\"small\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{mt:2,display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontWeight:\"bold\",children:[t('bookings.price'),\": \",(()=>{if(booking.price_paid!==null&&!isNaN(booking.price_paid)){return booking.price_paid.toFixed(2);}const basePrice=parseFloat(booking.price_per_lesson||0);const isDurationHalf=booking.duration==='25'||booking.duration===25;const finalPrice=isDurationHalf?basePrice/2:basePrice;return finalPrice.toFixed(2);})(),\" $\"]}),booking.status==='scheduled'&&/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"error\",size:\"small\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),onClick:()=>{setSelectedBooking(booking);setCancelDialogOpen(true);},children:t('bookings.cancel')})]})]})]})},booking.id))});};// Details dialog\nconst renderDetailsDialog=()=>/*#__PURE__*/_jsxs(Dialog,{open:detailsDialogOpen,onClose:()=>setDetailsDialogOpen(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsxs(DialogTitle,{children:[t('bookings.bookingDetails'),/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:()=>setDetailsDialogOpen(false),sx:{position:'absolute',right:8,top:8},children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(DialogContent,{children:selectedBooking&&/*#__PURE__*/_jsxs(Box,{sx:{py:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Avatar,{src:selectedBooking.teacher_picture?selectedBooking.teacher_picture.startsWith('http')?selectedBooking.teacher_picture:`https://allemnionline.com${selectedBooking.teacher_picture}`:'',alt:selectedBooking.teacher_name,sx:{width:120,height:120,border:`3px solid ${theme.palette.primary.main}`,mb:2}}),/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:selectedBooking.teacher_name}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:t('bookings.teacher')})]})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.date')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",gutterBottom:true,children:formatBookingDate(selectedBooking.datetime)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.time')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",gutterBottom:true,children:formatBookingTime(selectedBooking.datetime)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:1},children:formatBookingTimeRange(selectedBooking.datetime,parseInt(selectedBooking.duration)||50)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.duration')}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",gutterBottom:true,children:[selectedBooking.duration||50,\" \",t('bookings.minutes'),selectedBooking.duration==='25'||selectedBooking.duration===25?` (${t('booking.halfLesson')})`:` (${t('booking.fullLesson')})`]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.status.title')}),/*#__PURE__*/_jsx(Chip,{label:getStatusText(selectedBooking.status),color:getStatusColor(selectedBooking.status),size:\"small\"})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.price')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",color:\"primary\",children:[(()=>{if(selectedBooking.price_paid!==null&&!isNaN(selectedBooking.price_paid)){return selectedBooking.price_paid.toFixed(2);}const basePrice=parseFloat(selectedBooking.price_per_lesson||0);const isDurationHalf=selectedBooking.duration==='25'||selectedBooking.duration===25;const finalPrice=isDurationHalf?basePrice/2:basePrice;return finalPrice.toFixed(2);})(),\" \",t('common.currency')]})]})]})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailsDialogOpen(false),children:t('common.close')}),selectedBooking&&/*#__PURE__*/_jsx(Button,{onClick:()=>canJoinMeeting(selectedBooking)&&handleJoinMeeting(selectedBooking),color:canJoinMeeting(selectedBooking)?\"success\":\"inherit\",variant:canJoinMeeting(selectedBooking)?\"contained\":\"outlined\",startIcon:/*#__PURE__*/_jsx(VideoCallIcon,{}),disabled:!canJoinMeeting(selectedBooking),sx:{mr:1,...(canJoinMeeting(selectedBooking)?{}:{color:theme.palette.grey[500],borderColor:theme.palette.grey[300],backgroundColor:theme.palette.grey[100],'&:hover':{backgroundColor:theme.palette.grey[200],borderColor:theme.palette.grey[400]}})},children:getMeetingStatusText(selectedBooking)}),(selectedBooking===null||selectedBooking===void 0?void 0:selectedBooking.status)==='scheduled'&&/*#__PURE__*/_jsx(Button,{onClick:()=>{setDetailsDialogOpen(false);setCancelDialogOpen(true);},color:\"error\",variant:\"contained\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),children:t('bookings.cancel')})]})]});// Cancel confirmation dialog\nconst renderCancelDialog=()=>/*#__PURE__*/_jsxs(Dialog,{open:cancelDialogOpen,onClose:()=>setCancelDialogOpen(false),children:[/*#__PURE__*/_jsxs(DialogTitle,{children:[t('bookings.confirmCancel'),/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:()=>setCancelDialogOpen(false),sx:{position:'absolute',right:8,top:8},children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:t('bookings.cancelWarning')}),selectedBooking&&/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.teacher'),\":\"]}),\" \",selectedBooking.teacher_name]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.date'),\":\"]}),\" \",formatBookingDate(selectedBooking.datetime)]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.time'),\":\"]}),\" \",formatBookingTime(selectedBooking.datetime)]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,color:\"text.secondary\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.timeRange'),\":\"]}),\" \",formatBookingTimeRange(selectedBooking.datetime,parseInt(selectedBooking.duration)||50)]})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setCancelDialogOpen(false),children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{onClick:handleCancelBooking,color:\"error\",variant:\"contained\",disabled:cancellingBooking,children:cancellingBooking?t('bookings.cancelling'):t('bookings.confirmCancelButton')})]})]});return/*#__PURE__*/_jsxs(Layout,{children:[/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",sx:{py:4},children:/*#__PURE__*/_jsxs(ProfileCompletionAlert,{exemptPages:['/student/complete-profile','/student/dashboard'],children:[/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{p:3,mb:4,bgcolor:'primary.main',color:'white'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',flexWrap:'wrap',gap:2},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,sx:{fontWeight:'bold'},children:t('bookings.weeklyTitle')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{opacity:0.9},children:t('bookings.weeklyDescription')})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'right'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{opacity:0.8,mb:0.5},children:t('booking.weekNavigation')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:'bold'},children:[\"\\uD83D\\uDCC5 \",format(currentWeekStart,'MMM d',{locale:isRtl?ar:enUS}),\" - \",format(addDays(currentWeekStart,6),'MMM d, yyyy',{locale:isRtl?ar:enUS})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{opacity:0.8},children:[studentProfile!==null&&studentProfile!==void 0&&studentProfile.timezone?moment(formatDateInStudentTimezone(new Date().toISOString(),studentProfile.timezone,'YYYY-MM-DD HH:mm:ss'),'YYYY-MM-DD HH:mm:ss').format('h:mm A'):format(currentTime,'p',{locale:isRtl?ar:enUS}),(studentProfile===null||studentProfile===void 0?void 0:studentProfile.timezone)&&` (${studentProfile.timezone})`]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1},children:[/*#__PURE__*/_jsx(Tooltip,{title:t('booking.previousWeek'),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:goToPreviousWeek,disabled:isPreviousWeekDisabled(),sx:{color:'white',bgcolor:'rgba(255, 255, 255, 0.1)','&:hover':{bgcolor:'rgba(255, 255, 255, 0.2)'},'&:disabled':{color:'rgba(255, 255, 255, 0.3)',bgcolor:'rgba(255, 255, 255, 0.05)'}},children:/*#__PURE__*/_jsx(ChevronLeftIcon,{})})})}),/*#__PURE__*/_jsx(Tooltip,{title:t('booking.nextWeek'),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:goToNextWeek,disabled:isNextWeekDisabled(),sx:{color:'white',bgcolor:'rgba(255, 255, 255, 0.1)','&:hover':{bgcolor:'rgba(255, 255, 255, 0.2)'},'&:disabled':{color:'rgba(255, 255, 255, 0.3)',bgcolor:'rgba(255, 255, 255, 0.05)'}},children:/*#__PURE__*/_jsx(ChevronRightIcon,{})})})})]})]})]})}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',py:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}):error?/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:4},children:error}):/*#__PURE__*/_jsx(WeeklyBookingsTable,{bookings:bookings,loading:loading,currentWeekStart:currentWeekStart,daysOfWeek:daysOfWeek,onViewDetails:handleViewDetails,onCancelBooking:handleCancelBookingClick,studentProfile:studentProfile,formatBookingTime:formatBookingTime,getStatusColor:getStatusColor}),renderDetailsDialog(),renderCancelDialog()]})}),/*#__PURE__*/_jsx(Dialog,{fullScreen:true,open:openMeeting,onClose:handleCloseMeeting,children:currentMeeting&&/*#__PURE__*/_jsx(VideoSDKMeeting,{roomId:currentMeeting.room_name,meetingId:currentMeeting.meeting_id,meetingData:{...currentMeeting,meeting_date:currentMeeting.datetime,duration:parseInt(currentMeeting.duration)||50},onClose:handleCloseMeeting})}),/*#__PURE__*/_jsx(MeetingFeedbackDialog,{open:feedbackDialogOpen,meeting:feedbackMeeting,timezone:(studentProfile===null||studentProfile===void 0?void 0:studentProfile.timezone)||null,onSubmit:handleFeedbackSubmit,onClose:()=>{setFeedbackDialogOpen(false);setFeedbackMeeting(null);}})]});};export default Bookings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Container", "Typography", "Box", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "CircularProgress", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "IconButton", "Divider", "Avatar", "useTheme", "<PERSON><PERSON><PERSON>", "CalendarMonth", "CalendarIcon", "AccessTime", "TimeIcon", "Person", "PersonIcon", "Cancel", "CancelIcon", "Close", "CloseIcon", "Event", "EventIcon", "VideoCall", "VideoCallIcon", "ChevronLeft", "ChevronLeftIcon", "ChevronRight", "ChevronRightIcon", "format", "addDays", "startOfWeek", "addWeeks", "subWeeks", "ar", "enUS", "axios", "dayjs", "utc", "useAuth", "Layout", "WeeklyBookingsTable", "convertFromDatabaseTime", "formatDateInStudentTimezone", "getCurrentTimeInTimezone", "moment", "ProfileCompletionAlert", "VideoSDKMeeting", "MeetingFeedbackDialog", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "extend", "Bookings", "t", "i18n", "token", "theme", "isRtl", "language", "bookings", "setBookings", "loading", "setLoading", "error", "setError", "selectedBooking", "setSelectedBooking", "cancelDialogOpen", "setCancelDialogOpen", "detailsDialogOpen", "setDetailsDialogOpen", "cancellingBooking", "setCancellingBooking", "studentProfile", "setStudentProfile", "currentWeekStart", "setCurrentWeekStart", "today", "Date", "weekStartsOn", "openMeeting", "setOpenMeeting", "currentMeeting", "setCurrentMeeting", "currentTime", "setCurrentTime", "feedbackDialogOpen", "setFeedbackDialogOpen", "feedbackMeeting", "setFeedbackMeeting", "daysOfWeek", "goToPreviousWeek", "previousWeek", "goToNextWeek", "nextWeek", "oneYearAhead", "maxWeek", "isPreviousWeekDisabled", "isNextWeekDisabled", "fetchStudentProfile", "data", "get", "headers", "success", "profile", "console", "checkPending", "issue", "shouldOpen", "isAfter", "datetime", "add", "duration", "err", "booking_id", "length", "targetTime", "candidate", "find", "b", "teacher_name", "bTime", "Math", "abs", "diff", "prev", "id", "fetchBookings", "log", "processedBookings", "map", "booking", "price_per_lesson", "parseFloat", "price_paid", "undefined", "String", "message", "fetchWithRetry", "maxRetries", "arguments", "retries", "Promise", "resolve", "setTimeout", "timeInterval", "setInterval", "clearInterval", "handleViewDetails", "handleCancelBookingClick", "handleJoinMeeting", "room_name", "meeting_id", "response", "handleCloseMeeting", "meetingEnd", "setMinutes", "getMinutes", "now", "post", "issue_type", "description", "catch", "getCurrentTimeInStudentTimezone", "timezone", "getMeetingDateInStudentTimezone", "getMeetingStatus", "status", "canJoinMeeting", "currentStatus", "meetingStartTime", "meetingEndTime", "getMeetingStatusText", "canJoin", "handleCancelBooking", "put", "prevBookings", "_error$response", "_error$response$data", "getStatusColor", "getStatusText", "defaultValue", "char<PERSON>t", "toUpperCase", "slice", "formatBookingDate", "locale", "formattedDate", "formatBookingTime", "formattedDateTime", "formatBookingTimeRange", "startDate", "endDate", "getTime", "startTimeStr", "toLocaleTimeString", "hour", "minute", "hour12", "endTimeStr", "datePart", "timePart", "split", "hours", "minutes", "startMinutes", "parseInt", "endMinutes", "startHour", "floor", "startMin", "endHour", "endMin", "padStart", "handleFeedbackSubmit", "meetingId", "values", "newStatus", "renderBookings", "sx", "textAlign", "py", "children", "variant", "color", "container", "spacing", "item", "xs", "sm", "md", "elevation", "height", "display", "flexDirection", "bgcolor", "p", "alignItems", "gap", "flexGrow", "mb", "src", "teacher_picture", "alt", "mr", "my", "label", "size", "mt", "justifyContent", "fontWeight", "isNaN", "toFixed", "basePrice", "isDurationHalf", "finalPrice", "startIcon", "onClick", "renderDetailsDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "position", "right", "top", "startsWith", "width", "border", "palette", "primary", "main", "gutterBottom", "disabled", "grey", "borderColor", "backgroundColor", "renderCancelDialog", "exemptPages", "flexWrap", "opacity", "toISOString", "title", "severity", "onViewDetails", "onCancelBooking", "fullScreen", "roomId", "meetingData", "meeting_date", "meeting", "onSubmit"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/student/Bookings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Container,\n  Typography,\n  Box,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Button,\n  Chip,\n  CircularProgress,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  IconButton,\n  Divider,\n  Avatar,\n  useTheme,\n  Tooltip\n} from '@mui/material';\nimport {\n  CalendarMonth as CalendarIcon,\n  AccessTime as TimeIcon,\n  Person as PersonIcon,\n  Cancel as CancelIcon,\n  Close as CloseIcon,\n  Event as EventIcon,\n  VideoCall as VideoCallIcon,\n  ChevronLeft as ChevronLeftIcon,\n  ChevronRight as ChevronRightIcon\n} from '@mui/icons-material';\nimport { format, addDays, startOfWeek, addWeeks, subWeeks } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport axios from '../../utils/axios';\nimport dayjs from 'dayjs';\nimport utc from 'dayjs/plugin/utc';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Layout from '../../components/Layout';\nimport WeeklyBookingsTable from '../../components/WeeklyBookingsTable';\nimport { convertFromDatabaseTime, formatDateInStudentTimezone, getCurrentTimeInTimezone } from '../../utils/timezone';\nimport moment from 'moment-timezone';\nimport ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';\nimport VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';\nimport MeetingFeedbackDialog from '../../components/MeetingFeedbackDialog';\nimport { toast } from 'react-hot-toast';\n\ndayjs.extend(utc);\n\nconst Bookings = () => {\n  const { t, i18n } = useTranslation();\n  const { token } = useAuth();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n\n  const [bookings, setBookings] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedBooking, setSelectedBooking] = useState(null);\n  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n  const [cancellingBooking, setCancellingBooking] = useState(false);\n  const [studentProfile, setStudentProfile] = useState(null);\n  const [currentWeekStart, setCurrentWeekStart] = useState(() => {\n    const today = new Date();\n    return startOfWeek(today, { weekStartsOn: 1 }); // Start from current week\n  });\n  const [openMeeting, setOpenMeeting] = useState(false);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\n  const [feedbackMeeting, setFeedbackMeeting] = useState(null);\n\n  // Days of the week\n  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n\n  // Week navigation functions\n  const goToPreviousWeek = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    setCurrentWeekStart(previousWeek);\n  };\n\n  const goToNextWeek = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n\n    // Don't allow going beyond one year ahead\n    if (nextWeek <= maxWeek) {\n      setCurrentWeekStart(nextWeek);\n    }\n  };\n\n  // Check if navigation buttons should be disabled\n  const isPreviousWeekDisabled = () => false;\n\n  const isNextWeekDisabled = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n    return nextWeek > maxWeek;\n  };\n\n  // Fetch student profile\n  useEffect(() => {\n    const fetchStudentProfile = async () => {\n      if (!token) return;\n\n      try {\n        const { data } = await axios.get('/api/students/profile', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success && data.profile) {\n          setStudentProfile(data.profile);\n        }\n      } catch (error) {\n        console.error('Error fetching student profile:', error);\n      }\n    };\n\n    fetchStudentProfile();\n  }, [token]);\n\n  // Check for pending feedback on mount\n  useEffect(() => {\n    const checkPending = async () => {\n      try {\n        const { data } = await axios.get('/meeting-issues/pending');\n        if (data.success && data.data) {\n          const issue = data.data;\n          const shouldOpen = dayjs.utc().isAfter(dayjs.utc(issue.datetime).add(issue.duration || 50, 'minute'));\n          setFeedbackMeeting(issue);\n          setFeedbackDialogOpen(shouldOpen);\n        }\n      } catch (err) {\n        console.error('Error checking pending feedback', err);\n      }\n    };\n    checkPending();\n  }, []);\n\n  // Attach booking_id to feedback meeting if missing\n  useEffect(() => {\n    if (!feedbackMeeting || feedbackMeeting.booking_id) return;\n    if (!bookings.length) return;\n\n    const targetTime = moment(feedbackMeeting.datetime);\n    // find booking with same teacher and date (same day) within 2 hours window\n    const candidate = bookings.find(b => {\n      if (b.teacher_name !== feedbackMeeting.teacher_name) return false;\n      const bTime = moment(b.datetime);\n      return Math.abs(bTime.diff(targetTime, 'minutes')) <= 120;\n    });\n    if (candidate) {\n      setFeedbackMeeting(prev => ({ ...prev, booking_id: candidate.id }));\n    }\n  }, [bookings, feedbackMeeting]);\n\n  // Fetch bookings with retry logic\n  useEffect(() => {\n    const fetchBookings = async () => {\n      try {\n        setLoading(true);\n        const { data } = await axios.get('/bookings/student', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success) {\n          console.log('Bookings data:', data.data);\n\n          // Make sure all bookings have the correct data types\n          const processedBookings = data.data.map(booking => ({\n            ...booking,\n            price_per_lesson: parseFloat(booking.price_per_lesson || 0),\n            price_paid: booking.price_paid !== undefined ? parseFloat(booking.price_paid) : null,\n            duration: booking.duration ? String(booking.duration) : '50'\n          }));\n\n          console.log('Processed bookings:', processedBookings);\n          setBookings(processedBookings);\n        } else {\n          setError(data.message || t('bookings.fetchError'));\n        }\n      } catch (error) {\n        console.error('Error fetching bookings:', error);\n        setError(t('bookings.fetchError'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    const fetchWithRetry = async (maxRetries = 3) => {\n      let retries = 0;\n      while (retries < maxRetries) {\n        try {\n          await fetchBookings();\n          break;\n        } catch (error) {\n          retries++;\n          if (retries === maxRetries) {\n            throw error;\n          }\n          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait before retry\n        }\n      }\n    };\n\n    if (token) {\n      fetchWithRetry();\n    }\n\n    // Update current time every second\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timeInterval);\n  }, [token, t]);\n\n  // Handle view details\n  const handleViewDetails = (booking) => {\n    setSelectedBooking(booking);\n    setDetailsDialogOpen(true);\n  };\n\n  // Handle cancel booking\n  const handleCancelBookingClick = (booking) => {\n    setSelectedBooking(booking);\n    setCancelDialogOpen(true);\n  };\n\n  // Handle join meeting\n  const handleJoinMeeting = async (booking) => {\n    try {\n      // Check if room_name exists from the booking data\n      if (!booking.room_name) {\n        console.error('No room_name found for booking:', booking);\n        toast.error(t('meetings.noRoomError') || 'Meeting room not found');\n        return;\n      }\n\n      // Check if meeting_id exists\n      if (!booking.meeting_id) {\n        console.error('No meeting_id found for booking:', booking);\n        toast.error(t('meetings.noMeetingError') || 'Meeting ID not found');\n        return;\n      }\n\n      console.log('Joining meeting with data:', {\n        room_name: booking.room_name,\n        meeting_id: booking.meeting_id,\n        datetime: booking.datetime,\n        duration: booking.duration\n      });\n\n      // التحقق من صلاحية الغرفة\n      const response = await axios.get(`/meetings/${booking.room_name}/validate`);\n      setCurrentMeeting({ ...booking, room_name: booking.room_name });\n      setOpenMeeting(true);\n    } catch (error) {\n      console.error('Error joining meeting:', error);\n      toast.error(t('meetings.joinError'));\n    }\n  };\n\n  const handleCloseMeeting = () => {\n    // After meeting dialog closes, prompt for feedback\n    if (currentMeeting) {\n      const meetingEnd = new Date(currentMeeting.datetime);\n      meetingEnd.setMinutes(meetingEnd.getMinutes() + (currentMeeting.duration || 50));\n      const now = new Date();\n      // Only prompt if meeting time has actually ended\n      if (now >= meetingEnd) {\n        setFeedbackMeeting(currentMeeting);\n        // Send pending status immediately\n        axios.post('/meeting-issues', {\n          booking_id: currentMeeting.id,\n          meeting_id: currentMeeting.meeting_id || currentMeeting.id,\n          issue_type: 'pending',\n          description: ''\n        }).catch(err => console.error('Failed to create pending issue', err));\n        setFeedbackDialogOpen(true);\n      }\n    }\n    setOpenMeeting(false);\n    setCurrentMeeting(null);\n  };\n\n  // Get current time in student's timezone (same method as meetings page)\n  const getCurrentTimeInStudentTimezone = () => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return new Date();\n    }\n    return getCurrentTimeInTimezone(studentProfile.timezone);\n  };\n\n  // Get meeting date in student timezone (same method as meetings page)\n  const getMeetingDateInStudentTimezone = (datetime) => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return new Date(datetime);\n    }\n    return convertFromDatabaseTime(datetime, studentProfile.timezone);\n  };\n\n  // Get meeting status from database directly\n  const getMeetingStatus = (booking) => {\n    return booking.status || 'scheduled';\n  };\n\n  // Check if user can join meeting (same method as meetings page)\n  const canJoinMeeting = (booking) => {\n    if (!booking || !studentProfile) return false;\n\n    const currentStatus = getMeetingStatus(booking);\n    if (currentStatus === 'cancelled' || currentStatus === 'completed') {\n      return false;\n    }\n\n    const meetingStartTime = getMeetingDateInStudentTimezone(booking.datetime);\n    const meetingEndTime = new Date(meetingStartTime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + (booking.duration || 50));\n    const now = getCurrentTimeInStudentTimezone();\n\n    return now >= meetingStartTime && now < meetingEndTime;\n  };\n\n  // Get meeting status text (same method as meetings page)\n  const getMeetingStatusText = (booking) => {\n    if (!booking || !studentProfile) return t('meetings.notStarted');\n\n    const currentStatus = getMeetingStatus(booking);\n    const canJoin = canJoinMeeting(booking);\n\n    if (canJoin) {\n      return t('meetings.join');\n    }\n\n    switch (currentStatus) {\n      case 'cancelled':\n        return t('meetings.status.cancelled');\n      case 'completed':\n        return t('meetings.status.completed');\n      case 'ongoing':\n        return t('meetings.join');\n      case 'scheduled':\n      default:\n        return t('meetings.notStarted');\n    }\n  };\n\n  // Handle booking cancellation\n  const handleCancelBooking = async () => {\n    if (!selectedBooking) return;\n\n    try {\n      setCancellingBooking(true);\n      const { data } = await axios.put(`/bookings/${selectedBooking.id}/cancel`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        // Update the booking status in the local state\n        setBookings(prevBookings =>\n          prevBookings.map(booking =>\n            booking.id === selectedBooking.id\n              ? { ...booking, status: 'cancelled' }\n              : booking\n          )\n        );\n        toast.success(t('bookings.cancelSuccess'));\n      } else {\n        toast.error(data.message || t('bookings.cancelError'));\n      }\n    } catch (error) {\n      console.error('Error cancelling booking:', error);\n      toast.error(error.response?.data?.message || t('bookings.cancelError'));\n    } finally {\n      setCancellingBooking(false);\n      setCancelDialogOpen(false);\n      setDetailsDialogOpen(false);\n      setSelectedBooking(null);\n    }\n  };\n\n  // Get status chip color\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'scheduled':\n        return 'primary';\n      case 'completed':\n        return 'success';\n      case 'cancelled':\n        return 'error';\n      case 'issue_reported':\n        return 'warning';\n      case 'ongoing':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n\n  // Get translated status text\n  const getStatusText = (status) => {\n    return t(`bookings.statusValues.${status}`, { \n      defaultValue: status.charAt(0).toUpperCase() + status.slice(1) \n    });\n  };\n\n  // Format booking date in student's timezone\n  const formatBookingDate = (datetime) => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return format(new Date(datetime), 'PPP', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDate = formatDateInStudentTimezone(datetime, studentProfile.timezone, 'YYYY-MM-DD');\n    return moment(formattedDate, 'YYYY-MM-DD').format('MMMM D, YYYY');\n  };\n\n  // Format booking time in student's timezone\n  const formatBookingTime = (datetime) => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return format(new Date(datetime), 'p', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDateTime = formatDateInStudentTimezone(datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    return moment(formattedDateTime, 'YYYY-MM-DD HH:mm:ss').format('h:mm A');\n  };\n\n  // Format booking time range (start - end) in student's timezone\n  const formatBookingTimeRange = (datetime, duration) => {\n    if (!studentProfile || !studentProfile.timezone) {\n      const startDate = new Date(datetime);\n      const endDate = new Date(startDate.getTime() + duration * 60000);\n\n      const startTimeStr = startDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n      const endTimeStr = endDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n\n      return `${startTimeStr} - ${endTimeStr}`;\n    }\n\n    // Use student timezone for accurate time calculation\n    const formattedDateTime = formatDateInStudentTimezone(datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    const [datePart, timePart] = formattedDateTime.split(' ');\n    const [hours, minutes] = timePart.split(':');\n\n    // Calculate start and end times\n    const startMinutes = parseInt(hours) * 60 + parseInt(minutes);\n    const endMinutes = startMinutes + duration;\n\n    const startHour = Math.floor(startMinutes / 60);\n    const startMin = startMinutes % 60;\n    const endHour = Math.floor(endMinutes / 60);\n    const endMin = endMinutes % 60;\n\n    const startTimeStr = `${String(startHour).padStart(2, '0')}:${String(startMin).padStart(2, '0')}`;\n    const endTimeStr = `${String(endHour).padStart(2, '0')}:${String(endMin).padStart(2, '0')}`;\n\n    return `${startTimeStr} - ${endTimeStr}`;\n  };\n\n  // Submit feedback after meeting\n  const handleFeedbackSubmit = async (meetingId, values) => {\n    try {\n      const response = await axios.post('/meeting-issues', {\n        meeting_id: meetingId,\n        booking_id: values.booking_id,\n        issue_type: values.issue_type,\n        description: values.description,\n      });\n\n      // Update booking status locally for immediate UI feedback\n      if (values.booking_id) {\n        const newStatus = values.issue_type === 'no_issue' ? 'completed' : 'issue_reported';\n        setBookings(prev => prev.map(b => (\n          b.id === values.booking_id ? { ...b, status: newStatus } : b\n        )));\n      }\n    } catch (err) {\n      console.error('Failed to submit feedback', err);\n    } finally {\n      setFeedbackDialogOpen(false);\n      setFeedbackMeeting(null);\n    }\n  };\n\n  // Render booking cards\n  const renderBookings = () => {\n    if (bookings.length === 0) {\n      return (\n        <Box sx={{ textAlign: 'center', py: 4 }}>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            {t('bookings.noBookings')}\n          </Typography>\n        </Box>\n      );\n    }\n\n    return (\n      <Grid container spacing={3}>\n        {bookings.map((booking) => (\n          <Grid item xs={12} sm={6} md={4} key={booking.id}>\n            <Card elevation={3} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n              <Box sx={{\n                bgcolor: 'primary.main',\n                color: 'white',\n                p: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              }}>\n                <CalendarIcon />\n                <Typography variant=\"h6\">\n                  {formatBookingDate(booking.datetime)}\n                </Typography>\n              </Box>\n              <CardContent sx={{ flexGrow: 1 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <Avatar\n                    src={booking.teacher_picture}\n                    alt={booking.teacher_name}\n                    sx={{ mr: 2 }}\n                  />\n                  <Typography variant=\"subtitle1\">\n                    {booking.teacher_name}\n                  </Typography>\n                </Box>\n\n                <Divider sx={{ my: 2 }} />\n\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                  <Typography variant=\"body2\">\n                    {formatBookingTime(booking.datetime)}\n                  </Typography>\n                </Box>\n\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                  <Typography variant=\"body2\">\n                    {t('bookings.duration')}: {booking.duration || 50} {t('bookings.minutes')}\n                    {booking.duration === '25' || booking.duration === 25 ?\n                      ` (${t('booking.halfLesson') || 'نصف درس'})` :\n                      ` (${t('booking.fullLesson') || 'درس كامل'})`}\n                  </Typography>\n                </Box>\n\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <EventIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                  <Chip\n                    label={getStatusText(booking.status)}\n                    color={getStatusColor(booking.status)}\n                    size=\"small\"\n                  />\n                </Box>\n\n                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {t('bookings.price')}: {(() => {\n                      if (booking.price_paid !== null && !isNaN(booking.price_paid)) {\n                        return booking.price_paid.toFixed(2);\n                      }\n                      const basePrice = parseFloat(booking.price_per_lesson || 0);\n                      const isDurationHalf = booking.duration === '25' || booking.duration === 25;\n                      const finalPrice = isDurationHalf ? basePrice / 2 : basePrice;\n                      return finalPrice.toFixed(2);\n                    })()} $\n                  </Typography>\n\n                  {booking.status === 'scheduled' && (\n                    <Button\n                      variant=\"outlined\"\n                      color=\"error\"\n                      size=\"small\"\n                      startIcon={<CancelIcon />}\n                      onClick={() => {\n                        setSelectedBooking(booking);\n                        setCancelDialogOpen(true);\n                      }}\n                    >\n                      {t('bookings.cancel')}\n                    </Button>\n                  )}\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    );\n  };\n\n  // Details dialog\n  const renderDetailsDialog = () => (\n    <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {t('bookings.bookingDetails')}\n        <IconButton\n          aria-label=\"close\"\n          onClick={() => setDetailsDialogOpen(false)}\n          sx={{ position: 'absolute', right: 8, top: 8 }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n      <DialogContent>\n        {selectedBooking && (\n          <Box sx={{ py: 2 }}>\n            {/* Teacher Info */}\n            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>\n              <Avatar\n                src={selectedBooking.teacher_picture ? (\n                  selectedBooking.teacher_picture.startsWith('http')\n                    ? selectedBooking.teacher_picture\n                    : `https://allemnionline.com${selectedBooking.teacher_picture}`\n                ) : ''}\n                alt={selectedBooking.teacher_name}\n                sx={{\n                  width: 120,\n                  height: 120,\n                  border: `3px solid ${theme.palette.primary.main}`,\n                  mb: 2\n                }}\n              />\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h6\" gutterBottom>\n                  {selectedBooking.teacher_name}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {t('bookings.teacher')}\n                </Typography>\n              </Box>\n            </Box>\n\n            <Divider sx={{ my: 2 }} />\n\n            {/* Booking Details */}\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.date')}\n                </Typography>\n                <Typography variant=\"body1\" gutterBottom>\n                  {formatBookingDate(selectedBooking.datetime)}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.time')}\n                </Typography>\n                <Typography variant=\"body1\" gutterBottom>\n                  {formatBookingTime(selectedBooking.datetime)}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                  {formatBookingTimeRange(selectedBooking.datetime, parseInt(selectedBooking.duration) || 50)}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.duration')}\n                </Typography>\n                <Typography variant=\"body1\" gutterBottom>\n                  {selectedBooking.duration || 50} {t('bookings.minutes')}\n                  {selectedBooking.duration === '25' || selectedBooking.duration === 25 ?\n                    ` (${t('booking.halfLesson')})` :\n                    ` (${t('booking.fullLesson')})`}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.status.title')}\n                </Typography>\n                <Chip\n                  label={getStatusText(selectedBooking.status)}\n                  color={getStatusColor(selectedBooking.status)}\n                  size=\"small\"\n                />\n              </Grid>\n              <Grid item xs={12}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.price')}\n                </Typography>\n                <Typography variant=\"h6\" color=\"primary\">\n                  {(() => {\n                    if (selectedBooking.price_paid !== null && !isNaN(selectedBooking.price_paid)) {\n                      return selectedBooking.price_paid.toFixed(2);\n                    }\n                    const basePrice = parseFloat(selectedBooking.price_per_lesson || 0);\n                    const isDurationHalf = selectedBooking.duration === '25' || selectedBooking.duration === 25;\n                    const finalPrice = isDurationHalf ? basePrice / 2 : basePrice;\n                    return finalPrice.toFixed(2);\n                  })()} {t('common.currency')}\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={() => setDetailsDialogOpen(false)}>\n          {t('common.close')}\n        </Button>\n\n        {/* Join Meeting Button */}\n        {selectedBooking && (\n          <Button\n            onClick={() => canJoinMeeting(selectedBooking) && handleJoinMeeting(selectedBooking)}\n            color={canJoinMeeting(selectedBooking) ? \"success\" : \"inherit\"}\n            variant={canJoinMeeting(selectedBooking) ? \"contained\" : \"outlined\"}\n            startIcon={<VideoCallIcon />}\n            disabled={!canJoinMeeting(selectedBooking)}\n            sx={{\n              mr: 1,\n              ...(canJoinMeeting(selectedBooking) ? {} : {\n                color: theme.palette.grey[500],\n                borderColor: theme.palette.grey[300],\n                backgroundColor: theme.palette.grey[100],\n                '&:hover': {\n                  backgroundColor: theme.palette.grey[200],\n                  borderColor: theme.palette.grey[400]\n                }\n              })\n            }}\n          >\n            {getMeetingStatusText(selectedBooking)}\n          </Button>\n        )}\n\n        {selectedBooking?.status === 'scheduled' && (\n          <Button\n            onClick={() => {\n              setDetailsDialogOpen(false);\n              setCancelDialogOpen(true);\n            }}\n            color=\"error\"\n            variant=\"contained\"\n            startIcon={<CancelIcon />}\n          >\n            {t('bookings.cancel')}\n          </Button>\n        )}\n      </DialogActions>\n    </Dialog>\n  );\n\n  // Cancel confirmation dialog\n  const renderCancelDialog = () => (\n    <Dialog open={cancelDialogOpen} onClose={() => setCancelDialogOpen(false)}>\n      <DialogTitle>\n        {t('bookings.confirmCancel')}\n        <IconButton\n          aria-label=\"close\"\n          onClick={() => setCancelDialogOpen(false)}\n          sx={{ position: 'absolute', right: 8, top: 8 }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n      <DialogContent>\n        <Typography variant=\"body1\">\n          {t('bookings.cancelWarning')}\n        </Typography>\n        {selectedBooking && (\n          <Box sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.teacher')}:</strong> {selectedBooking.teacher_name}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.date')}:</strong> {formatBookingDate(selectedBooking.datetime)}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.time')}:</strong> {formatBookingTime(selectedBooking.datetime)}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom color=\"text.secondary\">\n              <strong>{t('bookings.timeRange')}:</strong> {formatBookingTimeRange(selectedBooking.datetime, parseInt(selectedBooking.duration) || 50)}\n            </Typography>\n          </Box>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={() => setCancelDialogOpen(false)}>\n          {t('common.cancel')}\n        </Button>\n        <Button\n          onClick={handleCancelBooking}\n          color=\"error\"\n          variant=\"contained\"\n          disabled={cancellingBooking}\n        >\n          {cancellingBooking ? t('bookings.cancelling') : t('bookings.confirmCancelButton')}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Layout>\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>\n          <Paper elevation={3} sx={{ p: 3, mb: 4, bgcolor: 'primary.main', color: 'white' }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>\n              <Box>\n                <Typography variant=\"h4\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                  {t('bookings.weeklyTitle')}\n                </Typography>\n                <Typography variant=\"body1\" sx={{ opacity: 0.9 }}>\n                  {t('bookings.weeklyDescription')}\n                </Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <Box sx={{ textAlign: 'right' }}>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 0.5 }}>\n                    {t('booking.weekNavigation')}\n                  </Typography>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                    📅 {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                    {studentProfile?.timezone ? (\n                      moment(formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('h:mm A')\n                    ) : (\n                      format(currentTime, 'p', {\n                        locale: isRtl ? ar : enUS\n                      })\n                    )}\n                    {studentProfile?.timezone && ` (${studentProfile.timezone})`}\n                  </Typography>\n                </Box>\n                <Box sx={{ display: 'flex', gap: 1 }}>\n                  <Tooltip title={t('booking.previousWeek')}>\n                    <span>\n                      <IconButton\n                        onClick={goToPreviousWeek}\n                        disabled={isPreviousWeekDisabled()}\n                        sx={{\n                          color: 'white',\n                          bgcolor: 'rgba(255, 255, 255, 0.1)',\n                          '&:hover': {\n                            bgcolor: 'rgba(255, 255, 255, 0.2)',\n                          },\n                          '&:disabled': {\n                            color: 'rgba(255, 255, 255, 0.3)',\n                            bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          }\n                        }}\n                      >\n                        <ChevronLeftIcon />\n                      </IconButton>\n                    </span>\n                  </Tooltip>\n                  <Tooltip title={t('booking.nextWeek')}>\n                    <span>\n                      <IconButton\n                        onClick={goToNextWeek}\n                        disabled={isNextWeekDisabled()}\n                        sx={{\n                          color: 'white',\n                          bgcolor: 'rgba(255, 255, 255, 0.1)',\n                          '&:hover': {\n                            bgcolor: 'rgba(255, 255, 255, 0.2)',\n                          },\n                          '&:disabled': {\n                            color: 'rgba(255, 255, 255, 0.3)',\n                            bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          }\n                        }}\n                      >\n                        <ChevronRightIcon />\n                      </IconButton>\n                    </span>\n                  </Tooltip>\n                </Box>\n              </Box>\n            </Box>\n          </Paper>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>\n            <CircularProgress />\n          </Box>\n        ) : error ? (\n          <Alert severity=\"error\" sx={{ mb: 4 }}>\n            {error}\n          </Alert>\n        ) : (\n          <WeeklyBookingsTable\n            bookings={bookings}\n            loading={loading}\n            currentWeekStart={currentWeekStart}\n            daysOfWeek={daysOfWeek}\n            onViewDetails={handleViewDetails}\n            onCancelBooking={handleCancelBookingClick}\n            studentProfile={studentProfile}\n            formatBookingTime={formatBookingTime}\n            getStatusColor={getStatusColor}\n          />\n        )}\n\n        {renderDetailsDialog()}\n        {renderCancelDialog()}\n        </ProfileCompletionAlert>\n      </Container>\n\n      {/* Meeting Dialog */}\n      <Dialog\n        fullScreen\n        open={openMeeting}\n        onClose={handleCloseMeeting}\n      >\n        {currentMeeting && (\n          <VideoSDKMeeting\n            roomId={currentMeeting.room_name}\n            meetingId={currentMeeting.meeting_id}\n            meetingData={{\n              ...currentMeeting,\n              meeting_date: currentMeeting.datetime,\n              duration: parseInt(currentMeeting.duration) || 50\n            }}\n            onClose={handleCloseMeeting}\n          />\n        )}\n      </Dialog>\n\n      {/* Feedback Dialog */}\n      <MeetingFeedbackDialog\n        open={feedbackDialogOpen}\n        meeting={feedbackMeeting}\n        timezone={studentProfile?.timezone || null}\n        onSubmit={handleFeedbackSubmit}\n        onClose={() => {\n          setFeedbackDialogOpen(false);\n          setFeedbackMeeting(null);\n        }}\n      />\n    </Layout>\n  );\n};\n\nexport default Bookings;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,MAAM,CACNC,IAAI,CACJC,gBAAgB,CAChBC,KAAK,CACLC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,UAAU,CACVC,OAAO,CACPC,MAAM,CACNC,QAAQ,CACRC,OAAO,KACF,eAAe,CACtB,OACEC,aAAa,GAAI,CAAAC,YAAY,CAC7BC,UAAU,GAAI,CAAAC,QAAQ,CACtBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,YAAY,GAAI,CAAAC,gBAAgB,KAC3B,qBAAqB,CAC5B,OAASC,MAAM,CAAEC,OAAO,CAAEC,WAAW,CAAEC,QAAQ,CAAEC,QAAQ,KAAQ,UAAU,CAC3E,OAASC,EAAE,CAAEC,IAAI,KAAQ,iBAAiB,CAC1C,MAAO,CAAAC,KAAK,KAAM,mBAAmB,CACrC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,GAAG,KAAM,kBAAkB,CAClC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,mBAAmB,KAAM,sCAAsC,CACtE,OAASC,uBAAuB,CAAEC,2BAA2B,CAAEC,wBAAwB,KAAQ,sBAAsB,CACrH,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CACpC,MAAO,CAAAC,sBAAsB,KAAM,iDAAiD,CACpF,MAAO,CAAAC,eAAe,KAAM,0CAA0C,CACtE,MAAO,CAAAC,qBAAqB,KAAM,wCAAwC,CAC1E,OAASC,KAAK,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExChB,KAAK,CAACiB,MAAM,CAAChB,GAAG,CAAC,CAEjB,KAAM,CAAAiB,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGnE,cAAc,CAAC,CAAC,CACpC,KAAM,CAAEoE,KAAM,CAAC,CAAGnB,OAAO,CAAC,CAAC,CAC3B,KAAM,CAAAoB,KAAK,CAAGlD,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAmD,KAAK,CAAGH,IAAI,CAACI,QAAQ,GAAK,IAAI,CAEpC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC4E,OAAO,CAAEC,UAAU,CAAC,CAAG7E,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8E,KAAK,CAAEC,QAAQ,CAAC,CAAG/E,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACgF,eAAe,CAAEC,kBAAkB,CAAC,CAAGjF,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACkF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnF,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACoF,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrF,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACsF,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvF,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACwF,cAAc,CAAEC,iBAAiB,CAAC,CAAGzF,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC0F,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3F,QAAQ,CAAC,IAAM,CAC7D,KAAM,CAAA4F,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACxB,MAAO,CAAAlD,WAAW,CAACiD,KAAK,CAAE,CAAEE,YAAY,CAAE,CAAE,CAAC,CAAC,CAAE;AAClD,CAAC,CAAC,CACF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGhG,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACiG,cAAc,CAAEC,iBAAiB,CAAC,CAAGlG,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACmG,WAAW,CAAEC,cAAc,CAAC,CAAGpG,QAAQ,CAAC,GAAI,CAAA6F,IAAI,CAAC,CAAC,CAAC,CAC1D,KAAM,CAACQ,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGtG,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACuG,eAAe,CAAEC,kBAAkB,CAAC,CAAGxG,QAAQ,CAAC,IAAI,CAAC,CAE5D;AACA,KAAM,CAAAyG,UAAU,CAAG,CAAC,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CAEjG;AACA,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,YAAY,CAAG9D,QAAQ,CAAC6C,gBAAgB,CAAE,CAAC,CAAC,CAClDC,mBAAmB,CAACgB,YAAY,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,QAAQ,CAAGjE,QAAQ,CAAC8C,gBAAgB,CAAE,CAAC,CAAC,CAC9C,KAAM,CAAAE,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAiB,YAAY,CAAGlE,QAAQ,CAACgD,KAAK,CAAE,EAAE,CAAC,CAAE;AAC1C,KAAM,CAAAmB,OAAO,CAAGpE,WAAW,CAACmE,YAAY,CAAE,CAAEhB,YAAY,CAAE,CAAE,CAAC,CAAC,CAE9D;AACA,GAAIe,QAAQ,EAAIE,OAAO,CAAE,CACvBpB,mBAAmB,CAACkB,QAAQ,CAAC,CAC/B,CACF,CAAC,CAED;AACA,KAAM,CAAAG,sBAAsB,CAAGA,CAAA,GAAM,KAAK,CAE1C,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAJ,QAAQ,CAAGjE,QAAQ,CAAC8C,gBAAgB,CAAE,CAAC,CAAC,CAC9C,KAAM,CAAAE,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAiB,YAAY,CAAGlE,QAAQ,CAACgD,KAAK,CAAE,EAAE,CAAC,CAAE;AAC1C,KAAM,CAAAmB,OAAO,CAAGpE,WAAW,CAACmE,YAAY,CAAE,CAAEhB,YAAY,CAAE,CAAE,CAAC,CAAC,CAC9D,MAAO,CAAAe,QAAQ,CAAGE,OAAO,CAC3B,CAAC,CAED;AACA9G,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiH,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAAC5C,KAAK,CAAE,OAEZ,GAAI,CACF,KAAM,CAAE6C,IAAK,CAAC,CAAG,KAAM,CAAAnE,KAAK,CAACoE,GAAG,CAAC,uBAAuB,CAAE,CACxDC,OAAO,CAAE,CACP,eAAe,CAAE,UAAU/C,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI6C,IAAI,CAACG,OAAO,EAAIH,IAAI,CAACI,OAAO,CAAE,CAChC9B,iBAAiB,CAAC0B,IAAI,CAACI,OAAO,CAAC,CACjC,CACF,CAAE,MAAOzC,KAAK,CAAE,CACd0C,OAAO,CAAC1C,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACzD,CACF,CAAC,CAEDoC,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,CAAC5C,KAAK,CAAC,CAAC,CAEX;AACArE,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwH,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAEN,IAAK,CAAC,CAAG,KAAM,CAAAnE,KAAK,CAACoE,GAAG,CAAC,yBAAyB,CAAC,CAC3D,GAAID,IAAI,CAACG,OAAO,EAAIH,IAAI,CAACA,IAAI,CAAE,CAC7B,KAAM,CAAAO,KAAK,CAAGP,IAAI,CAACA,IAAI,CACvB,KAAM,CAAAQ,UAAU,CAAG1E,KAAK,CAACC,GAAG,CAAC,CAAC,CAAC0E,OAAO,CAAC3E,KAAK,CAACC,GAAG,CAACwE,KAAK,CAACG,QAAQ,CAAC,CAACC,GAAG,CAACJ,KAAK,CAACK,QAAQ,EAAI,EAAE,CAAE,QAAQ,CAAC,CAAC,CACrGvB,kBAAkB,CAACkB,KAAK,CAAC,CACzBpB,qBAAqB,CAACqB,UAAU,CAAC,CACnC,CACF,CAAE,MAAOK,GAAG,CAAE,CACZR,OAAO,CAAC1C,KAAK,CAAC,iCAAiC,CAAEkD,GAAG,CAAC,CACvD,CACF,CAAC,CACDP,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN;AACAxH,SAAS,CAAC,IAAM,CACd,GAAI,CAACsG,eAAe,EAAIA,eAAe,CAAC0B,UAAU,CAAE,OACpD,GAAI,CAACvD,QAAQ,CAACwD,MAAM,CAAE,OAEtB,KAAM,CAAAC,UAAU,CAAG1E,MAAM,CAAC8C,eAAe,CAACsB,QAAQ,CAAC,CACnD;AACA,KAAM,CAAAO,SAAS,CAAG1D,QAAQ,CAAC2D,IAAI,CAACC,CAAC,EAAI,CACnC,GAAIA,CAAC,CAACC,YAAY,GAAKhC,eAAe,CAACgC,YAAY,CAAE,MAAO,MAAK,CACjE,KAAM,CAAAC,KAAK,CAAG/E,MAAM,CAAC6E,CAAC,CAACT,QAAQ,CAAC,CAChC,MAAO,CAAAY,IAAI,CAACC,GAAG,CAACF,KAAK,CAACG,IAAI,CAACR,UAAU,CAAE,SAAS,CAAC,CAAC,EAAI,GAAG,CAC3D,CAAC,CAAC,CACF,GAAIC,SAAS,CAAE,CACb5B,kBAAkB,CAACoC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEX,UAAU,CAAEG,SAAS,CAACS,EAAG,CAAC,CAAC,CAAC,CACrE,CACF,CAAC,CAAE,CAACnE,QAAQ,CAAE6B,eAAe,CAAC,CAAC,CAE/B;AACAtG,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6I,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFjE,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEsC,IAAK,CAAC,CAAG,KAAM,CAAAnE,KAAK,CAACoE,GAAG,CAAC,mBAAmB,CAAE,CACpDC,OAAO,CAAE,CACP,eAAe,CAAE,UAAU/C,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI6C,IAAI,CAACG,OAAO,CAAE,CAChBE,OAAO,CAACuB,GAAG,CAAC,gBAAgB,CAAE5B,IAAI,CAACA,IAAI,CAAC,CAExC;AACA,KAAM,CAAA6B,iBAAiB,CAAG7B,IAAI,CAACA,IAAI,CAAC8B,GAAG,CAACC,OAAO,GAAK,CAClD,GAAGA,OAAO,CACVC,gBAAgB,CAAEC,UAAU,CAACF,OAAO,CAACC,gBAAgB,EAAI,CAAC,CAAC,CAC3DE,UAAU,CAAEH,OAAO,CAACG,UAAU,GAAKC,SAAS,CAAGF,UAAU,CAACF,OAAO,CAACG,UAAU,CAAC,CAAG,IAAI,CACpFtB,QAAQ,CAAEmB,OAAO,CAACnB,QAAQ,CAAGwB,MAAM,CAACL,OAAO,CAACnB,QAAQ,CAAC,CAAG,IAC1D,CAAC,CAAC,CAAC,CAEHP,OAAO,CAACuB,GAAG,CAAC,qBAAqB,CAAEC,iBAAiB,CAAC,CACrDrE,WAAW,CAACqE,iBAAiB,CAAC,CAChC,CAAC,IAAM,CACLjE,QAAQ,CAACoC,IAAI,CAACqC,OAAO,EAAIpF,CAAC,CAAC,qBAAqB,CAAC,CAAC,CACpD,CACF,CAAE,MAAOU,KAAK,CAAE,CACd0C,OAAO,CAAC1C,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDC,QAAQ,CAACX,CAAC,CAAC,qBAAqB,CAAC,CAAC,CACpC,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA4E,cAAc,CAAG,cAAAA,CAAA,CAA0B,IAAnB,CAAAC,UAAU,CAAAC,SAAA,CAAAzB,MAAA,IAAAyB,SAAA,MAAAL,SAAA,CAAAK,SAAA,IAAG,CAAC,CAC1C,GAAI,CAAAC,OAAO,CAAG,CAAC,CACf,MAAOA,OAAO,CAAGF,UAAU,CAAE,CAC3B,GAAI,CACF,KAAM,CAAAZ,aAAa,CAAC,CAAC,CACrB,MACF,CAAE,MAAOhE,KAAK,CAAE,CACd8E,OAAO,EAAE,CACT,GAAIA,OAAO,GAAKF,UAAU,CAAE,CAC1B,KAAM,CAAA5E,KAAK,CACb,CACA,KAAM,IAAI,CAAA+E,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAAE;AAC3D,CACF,CACF,CAAC,CAED,GAAIxF,KAAK,CAAE,CACTmF,cAAc,CAAC,CAAC,CAClB,CAEA;AACA,KAAM,CAAAO,YAAY,CAAGC,WAAW,CAAC,IAAM,CACrC7D,cAAc,CAAC,GAAI,CAAAP,IAAI,CAAC,CAAC,CAAC,CAC5B,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMqE,aAAa,CAACF,YAAY,CAAC,CAC1C,CAAC,CAAE,CAAC1F,KAAK,CAAEF,CAAC,CAAC,CAAC,CAEd;AACA,KAAM,CAAA+F,iBAAiB,CAAIjB,OAAO,EAAK,CACrCjE,kBAAkB,CAACiE,OAAO,CAAC,CAC3B7D,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAA+E,wBAAwB,CAAIlB,OAAO,EAAK,CAC5CjE,kBAAkB,CAACiE,OAAO,CAAC,CAC3B/D,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAkF,iBAAiB,CAAG,KAAO,CAAAnB,OAAO,EAAK,CAC3C,GAAI,CACF;AACA,GAAI,CAACA,OAAO,CAACoB,SAAS,CAAE,CACtB9C,OAAO,CAAC1C,KAAK,CAAC,iCAAiC,CAAEoE,OAAO,CAAC,CACzDrF,KAAK,CAACiB,KAAK,CAACV,CAAC,CAAC,sBAAsB,CAAC,EAAI,wBAAwB,CAAC,CAClE,OACF,CAEA;AACA,GAAI,CAAC8E,OAAO,CAACqB,UAAU,CAAE,CACvB/C,OAAO,CAAC1C,KAAK,CAAC,kCAAkC,CAAEoE,OAAO,CAAC,CAC1DrF,KAAK,CAACiB,KAAK,CAACV,CAAC,CAAC,yBAAyB,CAAC,EAAI,sBAAsB,CAAC,CACnE,OACF,CAEAoD,OAAO,CAACuB,GAAG,CAAC,4BAA4B,CAAE,CACxCuB,SAAS,CAAEpB,OAAO,CAACoB,SAAS,CAC5BC,UAAU,CAAErB,OAAO,CAACqB,UAAU,CAC9B1C,QAAQ,CAAEqB,OAAO,CAACrB,QAAQ,CAC1BE,QAAQ,CAAEmB,OAAO,CAACnB,QACpB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAyC,QAAQ,CAAG,KAAM,CAAAxH,KAAK,CAACoE,GAAG,CAAC,aAAa8B,OAAO,CAACoB,SAAS,WAAW,CAAC,CAC3EpE,iBAAiB,CAAC,CAAE,GAAGgD,OAAO,CAAEoB,SAAS,CAAEpB,OAAO,CAACoB,SAAU,CAAC,CAAC,CAC/DtE,cAAc,CAAC,IAAI,CAAC,CACtB,CAAE,MAAOlB,KAAK,CAAE,CACd0C,OAAO,CAAC1C,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CjB,KAAK,CAACiB,KAAK,CAACV,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAqG,kBAAkB,CAAGA,CAAA,GAAM,CAC/B;AACA,GAAIxE,cAAc,CAAE,CAClB,KAAM,CAAAyE,UAAU,CAAG,GAAI,CAAA7E,IAAI,CAACI,cAAc,CAAC4B,QAAQ,CAAC,CACpD6C,UAAU,CAACC,UAAU,CAACD,UAAU,CAACE,UAAU,CAAC,CAAC,EAAI3E,cAAc,CAAC8B,QAAQ,EAAI,EAAE,CAAC,CAAC,CAChF,KAAM,CAAA8C,GAAG,CAAG,GAAI,CAAAhF,IAAI,CAAC,CAAC,CACtB;AACA,GAAIgF,GAAG,EAAIH,UAAU,CAAE,CACrBlE,kBAAkB,CAACP,cAAc,CAAC,CAClC;AACAjD,KAAK,CAAC8H,IAAI,CAAC,iBAAiB,CAAE,CAC5B7C,UAAU,CAAEhC,cAAc,CAAC4C,EAAE,CAC7B0B,UAAU,CAAEtE,cAAc,CAACsE,UAAU,EAAItE,cAAc,CAAC4C,EAAE,CAC1DkC,UAAU,CAAE,SAAS,CACrBC,WAAW,CAAE,EACf,CAAC,CAAC,CAACC,KAAK,CAACjD,GAAG,EAAIR,OAAO,CAAC1C,KAAK,CAAC,gCAAgC,CAAEkD,GAAG,CAAC,CAAC,CACrE1B,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CACF,CACAN,cAAc,CAAC,KAAK,CAAC,CACrBE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAAgF,+BAA+B,CAAGA,CAAA,GAAM,CAC5C,GAAI,CAAC1F,cAAc,EAAI,CAACA,cAAc,CAAC2F,QAAQ,CAAE,CAC/C,MAAO,IAAI,CAAAtF,IAAI,CAAC,CAAC,CACnB,CACA,MAAO,CAAArC,wBAAwB,CAACgC,cAAc,CAAC2F,QAAQ,CAAC,CAC1D,CAAC,CAED;AACA,KAAM,CAAAC,+BAA+B,CAAIvD,QAAQ,EAAK,CACpD,GAAI,CAACrC,cAAc,EAAI,CAACA,cAAc,CAAC2F,QAAQ,CAAE,CAC/C,MAAO,IAAI,CAAAtF,IAAI,CAACgC,QAAQ,CAAC,CAC3B,CACA,MAAO,CAAAvE,uBAAuB,CAACuE,QAAQ,CAAErC,cAAc,CAAC2F,QAAQ,CAAC,CACnE,CAAC,CAED;AACA,KAAM,CAAAE,gBAAgB,CAAInC,OAAO,EAAK,CACpC,MAAO,CAAAA,OAAO,CAACoC,MAAM,EAAI,WAAW,CACtC,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAIrC,OAAO,EAAK,CAClC,GAAI,CAACA,OAAO,EAAI,CAAC1D,cAAc,CAAE,MAAO,MAAK,CAE7C,KAAM,CAAAgG,aAAa,CAAGH,gBAAgB,CAACnC,OAAO,CAAC,CAC/C,GAAIsC,aAAa,GAAK,WAAW,EAAIA,aAAa,GAAK,WAAW,CAAE,CAClE,MAAO,MAAK,CACd,CAEA,KAAM,CAAAC,gBAAgB,CAAGL,+BAA+B,CAAClC,OAAO,CAACrB,QAAQ,CAAC,CAC1E,KAAM,CAAA6D,cAAc,CAAG,GAAI,CAAA7F,IAAI,CAAC4F,gBAAgB,CAAC,CACjDC,cAAc,CAACf,UAAU,CAACe,cAAc,CAACd,UAAU,CAAC,CAAC,EAAI1B,OAAO,CAACnB,QAAQ,EAAI,EAAE,CAAC,CAAC,CACjF,KAAM,CAAA8C,GAAG,CAAGK,+BAA+B,CAAC,CAAC,CAE7C,MAAO,CAAAL,GAAG,EAAIY,gBAAgB,EAAIZ,GAAG,CAAGa,cAAc,CACxD,CAAC,CAED;AACA,KAAM,CAAAC,oBAAoB,CAAIzC,OAAO,EAAK,CACxC,GAAI,CAACA,OAAO,EAAI,CAAC1D,cAAc,CAAE,MAAO,CAAApB,CAAC,CAAC,qBAAqB,CAAC,CAEhE,KAAM,CAAAoH,aAAa,CAAGH,gBAAgB,CAACnC,OAAO,CAAC,CAC/C,KAAM,CAAA0C,OAAO,CAAGL,cAAc,CAACrC,OAAO,CAAC,CAEvC,GAAI0C,OAAO,CAAE,CACX,MAAO,CAAAxH,CAAC,CAAC,eAAe,CAAC,CAC3B,CAEA,OAAQoH,aAAa,EACnB,IAAK,WAAW,CACd,MAAO,CAAApH,CAAC,CAAC,2BAA2B,CAAC,CACvC,IAAK,WAAW,CACd,MAAO,CAAAA,CAAC,CAAC,2BAA2B,CAAC,CACvC,IAAK,SAAS,CACZ,MAAO,CAAAA,CAAC,CAAC,eAAe,CAAC,CAC3B,IAAK,WAAW,CAChB,QACE,MAAO,CAAAA,CAAC,CAAC,qBAAqB,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAAyH,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAAC7G,eAAe,CAAE,OAEtB,GAAI,CACFO,oBAAoB,CAAC,IAAI,CAAC,CAC1B,KAAM,CAAE4B,IAAK,CAAC,CAAG,KAAM,CAAAnE,KAAK,CAAC8I,GAAG,CAAC,aAAa9G,eAAe,CAAC6D,EAAE,SAAS,CAAE,CAAC,CAAC,CAAE,CAC7ExB,OAAO,CAAE,CACP,eAAe,CAAE,UAAU/C,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI6C,IAAI,CAACG,OAAO,CAAE,CAChB;AACA3C,WAAW,CAACoH,YAAY,EACtBA,YAAY,CAAC9C,GAAG,CAACC,OAAO,EACtBA,OAAO,CAACL,EAAE,GAAK7D,eAAe,CAAC6D,EAAE,CAC7B,CAAE,GAAGK,OAAO,CAAEoC,MAAM,CAAE,WAAY,CAAC,CACnCpC,OACN,CACF,CAAC,CACDrF,KAAK,CAACyD,OAAO,CAAClD,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAC5C,CAAC,IAAM,CACLP,KAAK,CAACiB,KAAK,CAACqC,IAAI,CAACqC,OAAO,EAAIpF,CAAC,CAAC,sBAAsB,CAAC,CAAC,CACxD,CACF,CAAE,MAAOU,KAAK,CAAE,KAAAkH,eAAA,CAAAC,oBAAA,CACdzE,OAAO,CAAC1C,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDjB,KAAK,CAACiB,KAAK,CAAC,EAAAkH,eAAA,CAAAlH,KAAK,CAAC0F,QAAQ,UAAAwB,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgB7E,IAAI,UAAA8E,oBAAA,iBAApBA,oBAAA,CAAsBzC,OAAO,GAAIpF,CAAC,CAAC,sBAAsB,CAAC,CAAC,CACzE,CAAC,OAAS,CACRmB,oBAAoB,CAAC,KAAK,CAAC,CAC3BJ,mBAAmB,CAAC,KAAK,CAAC,CAC1BE,oBAAoB,CAAC,KAAK,CAAC,CAC3BJ,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAAiH,cAAc,CAAIZ,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,WAAW,CACd,MAAO,OAAO,CAChB,IAAK,gBAAgB,CACnB,MAAO,SAAS,CAClB,IAAK,SAAS,CACZ,MAAO,MAAM,CACf,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED;AACA,KAAM,CAAAa,aAAa,CAAIb,MAAM,EAAK,CAChC,MAAO,CAAAlH,CAAC,CAAC,yBAAyBkH,MAAM,EAAE,CAAE,CAC1Cc,YAAY,CAAEd,MAAM,CAACe,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGhB,MAAM,CAACiB,KAAK,CAAC,CAAC,CAC/D,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAI3E,QAAQ,EAAK,CACtC,GAAI,CAACrC,cAAc,EAAI,CAACA,cAAc,CAAC2F,QAAQ,CAAE,CAC/C,MAAO,CAAA1I,MAAM,CAAC,GAAI,CAAAoD,IAAI,CAACgC,QAAQ,CAAC,CAAE,KAAK,CAAE,CAAE4E,MAAM,CAAEjI,KAAK,CAAG1B,EAAE,CAAGC,IAAK,CAAC,CAAC,CACzE,CAEA,KAAM,CAAA2J,aAAa,CAAGnJ,2BAA2B,CAACsE,QAAQ,CAAErC,cAAc,CAAC2F,QAAQ,CAAE,YAAY,CAAC,CAClG,MAAO,CAAA1H,MAAM,CAACiJ,aAAa,CAAE,YAAY,CAAC,CAACjK,MAAM,CAAC,cAAc,CAAC,CACnE,CAAC,CAED;AACA,KAAM,CAAAkK,iBAAiB,CAAI9E,QAAQ,EAAK,CACtC,GAAI,CAACrC,cAAc,EAAI,CAACA,cAAc,CAAC2F,QAAQ,CAAE,CAC/C,MAAO,CAAA1I,MAAM,CAAC,GAAI,CAAAoD,IAAI,CAACgC,QAAQ,CAAC,CAAE,GAAG,CAAE,CAAE4E,MAAM,CAAEjI,KAAK,CAAG1B,EAAE,CAAGC,IAAK,CAAC,CAAC,CACvE,CAEA,KAAM,CAAA6J,iBAAiB,CAAGrJ,2BAA2B,CAACsE,QAAQ,CAAErC,cAAc,CAAC2F,QAAQ,CAAE,qBAAqB,CAAC,CAC/G,MAAO,CAAA1H,MAAM,CAACmJ,iBAAiB,CAAE,qBAAqB,CAAC,CAACnK,MAAM,CAAC,QAAQ,CAAC,CAC1E,CAAC,CAED;AACA,KAAM,CAAAoK,sBAAsB,CAAGA,CAAChF,QAAQ,CAAEE,QAAQ,GAAK,CACrD,GAAI,CAACvC,cAAc,EAAI,CAACA,cAAc,CAAC2F,QAAQ,CAAE,CAC/C,KAAM,CAAA2B,SAAS,CAAG,GAAI,CAAAjH,IAAI,CAACgC,QAAQ,CAAC,CACpC,KAAM,CAAAkF,OAAO,CAAG,GAAI,CAAAlH,IAAI,CAACiH,SAAS,CAACE,OAAO,CAAC,CAAC,CAAGjF,QAAQ,CAAG,KAAK,CAAC,CAEhE,KAAM,CAAAkF,YAAY,CAAGH,SAAS,CAACI,kBAAkB,CAAC,EAAE,CAAE,CACpDC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,KACV,CAAC,CAAC,CACF,KAAM,CAAAC,UAAU,CAAGP,OAAO,CAACG,kBAAkB,CAAC,EAAE,CAAE,CAChDC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,KACV,CAAC,CAAC,CAEF,MAAO,GAAGJ,YAAY,MAAMK,UAAU,EAAE,CAC1C,CAEA;AACA,KAAM,CAAAV,iBAAiB,CAAGrJ,2BAA2B,CAACsE,QAAQ,CAAErC,cAAc,CAAC2F,QAAQ,CAAE,qBAAqB,CAAC,CAC/G,KAAM,CAACoC,QAAQ,CAAEC,QAAQ,CAAC,CAAGZ,iBAAiB,CAACa,KAAK,CAAC,GAAG,CAAC,CACzD,KAAM,CAACC,KAAK,CAAEC,OAAO,CAAC,CAAGH,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAE5C;AACA,KAAM,CAAAG,YAAY,CAAGC,QAAQ,CAACH,KAAK,CAAC,CAAG,EAAE,CAAGG,QAAQ,CAACF,OAAO,CAAC,CAC7D,KAAM,CAAAG,UAAU,CAAGF,YAAY,CAAG7F,QAAQ,CAE1C,KAAM,CAAAgG,SAAS,CAAGtF,IAAI,CAACuF,KAAK,CAACJ,YAAY,CAAG,EAAE,CAAC,CAC/C,KAAM,CAAAK,QAAQ,CAAGL,YAAY,CAAG,EAAE,CAClC,KAAM,CAAAM,OAAO,CAAGzF,IAAI,CAACuF,KAAK,CAACF,UAAU,CAAG,EAAE,CAAC,CAC3C,KAAM,CAAAK,MAAM,CAAGL,UAAU,CAAG,EAAE,CAE9B,KAAM,CAAAb,YAAY,CAAG,GAAG1D,MAAM,CAACwE,SAAS,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAI7E,MAAM,CAAC0E,QAAQ,CAAC,CAACG,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACjG,KAAM,CAAAd,UAAU,CAAG,GAAG/D,MAAM,CAAC2E,OAAO,CAAC,CAACE,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAI7E,MAAM,CAAC4E,MAAM,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAE3F,MAAO,GAAGnB,YAAY,MAAMK,UAAU,EAAE,CAC1C,CAAC,CAED;AACA,KAAM,CAAAe,oBAAoB,CAAG,KAAAA,CAAOC,SAAS,CAAEC,MAAM,GAAK,CACxD,GAAI,CACF,KAAM,CAAA/D,QAAQ,CAAG,KAAM,CAAAxH,KAAK,CAAC8H,IAAI,CAAC,iBAAiB,CAAE,CACnDP,UAAU,CAAE+D,SAAS,CACrBrG,UAAU,CAAEsG,MAAM,CAACtG,UAAU,CAC7B8C,UAAU,CAAEwD,MAAM,CAACxD,UAAU,CAC7BC,WAAW,CAAEuD,MAAM,CAACvD,WACtB,CAAC,CAAC,CAEF;AACA,GAAIuD,MAAM,CAACtG,UAAU,CAAE,CACrB,KAAM,CAAAuG,SAAS,CAAGD,MAAM,CAACxD,UAAU,GAAK,UAAU,CAAG,WAAW,CAAG,gBAAgB,CACnFpG,WAAW,CAACiE,IAAI,EAAIA,IAAI,CAACK,GAAG,CAACX,CAAC,EAC5BA,CAAC,CAACO,EAAE,GAAK0F,MAAM,CAACtG,UAAU,CAAG,CAAE,GAAGK,CAAC,CAAEgD,MAAM,CAAEkD,SAAU,CAAC,CAAGlG,CAC5D,CAAC,CAAC,CACL,CACF,CAAE,MAAON,GAAG,CAAE,CACZR,OAAO,CAAC1C,KAAK,CAAC,2BAA2B,CAAEkD,GAAG,CAAC,CACjD,CAAC,OAAS,CACR1B,qBAAqB,CAAC,KAAK,CAAC,CAC5BE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAAiI,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI/J,QAAQ,CAACwD,MAAM,GAAK,CAAC,CAAE,CACzB,mBACEnE,IAAA,CAAC1D,GAAG,EAACqO,EAAE,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cACtC9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAAAF,QAAA,CAC/CzK,CAAC,CAAC,qBAAqB,CAAC,CACf,CAAC,CACV,CAAC,CAEV,CAEA,mBACEL,IAAA,CAACxD,IAAI,EAACyO,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAJ,QAAA,CACxBnK,QAAQ,CAACuE,GAAG,CAAEC,OAAO,eACpBnF,IAAA,CAACxD,IAAI,EAAC2O,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAR,QAAA,cAC9B5K,KAAA,CAACzD,IAAI,EAAC8O,SAAS,CAAE,CAAE,CAACZ,EAAE,CAAE,CAAEa,MAAM,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAS,CAAE,CAAAZ,QAAA,eACnF5K,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CACPgB,OAAO,CAAE,cAAc,CACvBX,KAAK,CAAE,OAAO,CACdY,CAAC,CAAE,CAAC,CACJH,OAAO,CAAE,MAAM,CACfI,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CACP,CAAE,CAAAhB,QAAA,eACA9K,IAAA,CAACvC,YAAY,GAAE,CAAC,cAChBuC,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,IAAI,CAAAD,QAAA,CACrBrC,iBAAiB,CAACtD,OAAO,CAACrB,QAAQ,CAAC,CAC1B,CAAC,EACV,CAAC,cACN5D,KAAA,CAACxD,WAAW,EAACiO,EAAE,CAAE,CAAEoB,QAAQ,CAAE,CAAE,CAAE,CAAAjB,QAAA,eAC/B5K,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,eACxD9K,IAAA,CAAC3C,MAAM,EACL4O,GAAG,CAAE9G,OAAO,CAAC+G,eAAgB,CAC7BC,GAAG,CAAEhH,OAAO,CAACX,YAAa,CAC1BmG,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFpM,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,WAAW,CAAAD,QAAA,CAC5B3F,OAAO,CAACX,YAAY,CACX,CAAC,EACV,CAAC,cAENxE,IAAA,CAAC5C,OAAO,EAACuN,EAAE,CAAE,CAAE0B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1BnM,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,eACxD9K,IAAA,CAACrC,QAAQ,EAACgN,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAC,CAAEpB,KAAK,CAAE,gBAAiB,CAAE,CAAE,CAAC,cACpDhL,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAAAD,QAAA,CACxBlC,iBAAiB,CAACzD,OAAO,CAACrB,QAAQ,CAAC,CAC1B,CAAC,EACV,CAAC,cAEN5D,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,eACxD9K,IAAA,CAACrC,QAAQ,EAACgN,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAC,CAAEpB,KAAK,CAAE,gBAAiB,CAAE,CAAE,CAAC,cACpD9K,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAAAD,QAAA,EACxBzK,CAAC,CAAC,mBAAmB,CAAC,CAAC,IAAE,CAAC8E,OAAO,CAACnB,QAAQ,EAAI,EAAE,CAAC,GAAC,CAAC3D,CAAC,CAAC,kBAAkB,CAAC,CACxE8E,OAAO,CAACnB,QAAQ,GAAK,IAAI,EAAImB,OAAO,CAACnB,QAAQ,GAAK,EAAE,CACnD,KAAK3D,CAAC,CAAC,oBAAoB,CAAC,EAAI,SAAS,GAAG,CAC5C,KAAKA,CAAC,CAAC,oBAAoB,CAAC,EAAI,UAAU,GAAG,EACrC,CAAC,EACV,CAAC,cAENH,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,eACxD9K,IAAA,CAAC7B,SAAS,EAACwM,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAC,CAAEpB,KAAK,CAAE,gBAAiB,CAAE,CAAE,CAAC,cACrDhL,IAAA,CAACpD,IAAI,EACH0P,KAAK,CAAElE,aAAa,CAACjD,OAAO,CAACoC,MAAM,CAAE,CACrCyD,KAAK,CAAE7C,cAAc,CAAChD,OAAO,CAACoC,MAAM,CAAE,CACtCgF,IAAI,CAAC,OAAO,CACb,CAAC,EACC,CAAC,cAENrM,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAC,CAAEf,OAAO,CAAE,MAAM,CAAEgB,cAAc,CAAE,eAAe,CAAEZ,UAAU,CAAE,QAAS,CAAE,CAAAf,QAAA,eACzF5K,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAAC2B,UAAU,CAAC,MAAM,CAAA5B,QAAA,EAC1CzK,CAAC,CAAC,gBAAgB,CAAC,CAAC,IAAE,CAAC,CAAC,IAAM,CAC7B,GAAI8E,OAAO,CAACG,UAAU,GAAK,IAAI,EAAI,CAACqH,KAAK,CAACxH,OAAO,CAACG,UAAU,CAAC,CAAE,CAC7D,MAAO,CAAAH,OAAO,CAACG,UAAU,CAACsH,OAAO,CAAC,CAAC,CAAC,CACtC,CACA,KAAM,CAAAC,SAAS,CAAGxH,UAAU,CAACF,OAAO,CAACC,gBAAgB,EAAI,CAAC,CAAC,CAC3D,KAAM,CAAA0H,cAAc,CAAG3H,OAAO,CAACnB,QAAQ,GAAK,IAAI,EAAImB,OAAO,CAACnB,QAAQ,GAAK,EAAE,CAC3E,KAAM,CAAA+I,UAAU,CAAGD,cAAc,CAAGD,SAAS,CAAG,CAAC,CAAGA,SAAS,CAC7D,MAAO,CAAAE,UAAU,CAACH,OAAO,CAAC,CAAC,CAAC,CAC9B,CAAC,EAAE,CAAC,CAAC,IACP,EAAY,CAAC,CAEZzH,OAAO,CAACoC,MAAM,GAAK,WAAW,eAC7BvH,IAAA,CAACrD,MAAM,EACLoO,OAAO,CAAC,UAAU,CAClBC,KAAK,CAAC,OAAO,CACbuB,IAAI,CAAC,OAAO,CACZS,SAAS,cAAEhN,IAAA,CAACjC,UAAU,GAAE,CAAE,CAC1BkP,OAAO,CAAEA,CAAA,GAAM,CACb/L,kBAAkB,CAACiE,OAAO,CAAC,CAC3B/D,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CAAA0J,QAAA,CAEDzK,CAAC,CAAC,iBAAiB,CAAC,CACf,CACT,EACE,CAAC,EACK,CAAC,EACV,CAAC,EApF6B8E,OAAO,CAACL,EAqFxC,CACP,CAAC,CACE,CAAC,CAEX,CAAC,CAED;AACA,KAAM,CAAAoI,mBAAmB,CAAGA,CAAA,gBAC1BhN,KAAA,CAACnD,MAAM,EAACoQ,IAAI,CAAE9L,iBAAkB,CAAC+L,OAAO,CAAEA,CAAA,GAAM9L,oBAAoB,CAAC,KAAK,CAAE,CAAC+L,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAxC,QAAA,eAClG5K,KAAA,CAAClD,WAAW,EAAA8N,QAAA,EACTzK,CAAC,CAAC,yBAAyB,CAAC,cAC7BL,IAAA,CAAC7C,UAAU,EACT,aAAW,OAAO,CAClB8P,OAAO,CAAEA,CAAA,GAAM3L,oBAAoB,CAAC,KAAK,CAAE,CAC3CqJ,EAAE,CAAE,CAAE4C,QAAQ,CAAE,UAAU,CAAEC,KAAK,CAAE,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA3C,QAAA,cAE/C9K,IAAA,CAAC/B,SAAS,GAAE,CAAC,CACH,CAAC,EACF,CAAC,cACd+B,IAAA,CAAC/C,aAAa,EAAA6N,QAAA,CACX7J,eAAe,eACdf,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eAEjB5K,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEG,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,eACjF9K,IAAA,CAAC3C,MAAM,EACL4O,GAAG,CAAEhL,eAAe,CAACiL,eAAe,CAClCjL,eAAe,CAACiL,eAAe,CAACwB,UAAU,CAAC,MAAM,CAAC,CAC9CzM,eAAe,CAACiL,eAAe,CAC/B,4BAA4BjL,eAAe,CAACiL,eAAe,EAAE,CAC/D,EAAG,CACPC,GAAG,CAAElL,eAAe,CAACuD,YAAa,CAClCmG,EAAE,CAAE,CACFgD,KAAK,CAAE,GAAG,CACVnC,MAAM,CAAE,GAAG,CACXoC,MAAM,CAAE,aAAapN,KAAK,CAACqN,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,CACjD/B,EAAE,CAAE,CACN,CAAE,CACH,CAAC,cACF9L,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAE,QAAA,eAC/B9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,IAAI,CAACiD,YAAY,MAAAlD,QAAA,CAClC7J,eAAe,CAACuD,YAAY,CACnB,CAAC,cACbxE,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAAAF,QAAA,CAC/CzK,CAAC,CAAC,kBAAkB,CAAC,CACZ,CAAC,EACV,CAAC,EACH,CAAC,cAENL,IAAA,CAAC5C,OAAO,EAACuN,EAAE,CAAE,CAAE0B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BnM,KAAA,CAAC1D,IAAI,EAACyO,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAJ,QAAA,eACzB5K,KAAA,CAAC1D,IAAI,EAAC2O,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAP,QAAA,eACvB9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAACgD,YAAY,MAAAlD,QAAA,CAC5DzK,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbL,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACiD,YAAY,MAAAlD,QAAA,CACrCrC,iBAAiB,CAACxH,eAAe,CAAC6C,QAAQ,CAAC,CAClC,CAAC,EACT,CAAC,cACP5D,KAAA,CAAC1D,IAAI,EAAC2O,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAP,QAAA,eACvB9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAACgD,YAAY,MAAAlD,QAAA,CAC5DzK,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbL,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACiD,YAAY,MAAAlD,QAAA,CACrClC,iBAAiB,CAAC3H,eAAe,CAAC6C,QAAQ,CAAC,CAClC,CAAC,cACb9D,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAACL,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAC9DhC,sBAAsB,CAAC7H,eAAe,CAAC6C,QAAQ,CAAEgG,QAAQ,CAAC7I,eAAe,CAAC+C,QAAQ,CAAC,EAAI,EAAE,CAAC,CACjF,CAAC,EACT,CAAC,cACP9D,KAAA,CAAC1D,IAAI,EAAC2O,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAP,QAAA,eACvB9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAACgD,YAAY,MAAAlD,QAAA,CAC5DzK,CAAC,CAAC,mBAAmB,CAAC,CACb,CAAC,cACbH,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACiD,YAAY,MAAAlD,QAAA,EACrC7J,eAAe,CAAC+C,QAAQ,EAAI,EAAE,CAAC,GAAC,CAAC3D,CAAC,CAAC,kBAAkB,CAAC,CACtDY,eAAe,CAAC+C,QAAQ,GAAK,IAAI,EAAI/C,eAAe,CAAC+C,QAAQ,GAAK,EAAE,CACnE,KAAK3D,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAC/B,KAAKA,CAAC,CAAC,oBAAoB,CAAC,GAAG,EACvB,CAAC,EACT,CAAC,cACPH,KAAA,CAAC1D,IAAI,EAAC2O,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAP,QAAA,eACvB9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAACgD,YAAY,MAAAlD,QAAA,CAC5DzK,CAAC,CAAC,uBAAuB,CAAC,CACjB,CAAC,cACbL,IAAA,CAACpD,IAAI,EACH0P,KAAK,CAAElE,aAAa,CAACnH,eAAe,CAACsG,MAAM,CAAE,CAC7CyD,KAAK,CAAE7C,cAAc,CAAClH,eAAe,CAACsG,MAAM,CAAE,CAC9CgF,IAAI,CAAC,OAAO,CACb,CAAC,EACE,CAAC,cACPrM,KAAA,CAAC1D,IAAI,EAAC2O,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAN,QAAA,eAChB9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAACgD,YAAY,MAAAlD,QAAA,CAC5DzK,CAAC,CAAC,gBAAgB,CAAC,CACV,CAAC,cACbH,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,IAAI,CAACC,KAAK,CAAC,SAAS,CAAAF,QAAA,EACrC,CAAC,IAAM,CACN,GAAI7J,eAAe,CAACqE,UAAU,GAAK,IAAI,EAAI,CAACqH,KAAK,CAAC1L,eAAe,CAACqE,UAAU,CAAC,CAAE,CAC7E,MAAO,CAAArE,eAAe,CAACqE,UAAU,CAACsH,OAAO,CAAC,CAAC,CAAC,CAC9C,CACA,KAAM,CAAAC,SAAS,CAAGxH,UAAU,CAACpE,eAAe,CAACmE,gBAAgB,EAAI,CAAC,CAAC,CACnE,KAAM,CAAA0H,cAAc,CAAG7L,eAAe,CAAC+C,QAAQ,GAAK,IAAI,EAAI/C,eAAe,CAAC+C,QAAQ,GAAK,EAAE,CAC3F,KAAM,CAAA+I,UAAU,CAAGD,cAAc,CAAGD,SAAS,CAAG,CAAC,CAAGA,SAAS,CAC7D,MAAO,CAAAE,UAAU,CAACH,OAAO,CAAC,CAAC,CAAC,CAC9B,CAAC,EAAE,CAAC,CAAC,GAAC,CAACvM,CAAC,CAAC,iBAAiB,CAAC,EACjB,CAAC,EACT,CAAC,EACH,CAAC,EACJ,CACN,CACY,CAAC,cAChBH,KAAA,CAAChD,aAAa,EAAA4N,QAAA,eACZ9K,IAAA,CAACrD,MAAM,EAACsQ,OAAO,CAAEA,CAAA,GAAM3L,oBAAoB,CAAC,KAAK,CAAE,CAAAwJ,QAAA,CAChDzK,CAAC,CAAC,cAAc,CAAC,CACZ,CAAC,CAGRY,eAAe,eACdjB,IAAA,CAACrD,MAAM,EACLsQ,OAAO,CAAEA,CAAA,GAAMzF,cAAc,CAACvG,eAAe,CAAC,EAAIqF,iBAAiB,CAACrF,eAAe,CAAE,CACrF+J,KAAK,CAAExD,cAAc,CAACvG,eAAe,CAAC,CAAG,SAAS,CAAG,SAAU,CAC/D8J,OAAO,CAAEvD,cAAc,CAACvG,eAAe,CAAC,CAAG,WAAW,CAAG,UAAW,CACpE+L,SAAS,cAAEhN,IAAA,CAAC3B,aAAa,GAAE,CAAE,CAC7B4P,QAAQ,CAAE,CAACzG,cAAc,CAACvG,eAAe,CAAE,CAC3C0J,EAAE,CAAE,CACFyB,EAAE,CAAE,CAAC,CACL,IAAI5E,cAAc,CAACvG,eAAe,CAAC,CAAG,CAAC,CAAC,CAAG,CACzC+J,KAAK,CAAExK,KAAK,CAACqN,OAAO,CAACK,IAAI,CAAC,GAAG,CAAC,CAC9BC,WAAW,CAAE3N,KAAK,CAACqN,OAAO,CAACK,IAAI,CAAC,GAAG,CAAC,CACpCE,eAAe,CAAE5N,KAAK,CAACqN,OAAO,CAACK,IAAI,CAAC,GAAG,CAAC,CACxC,SAAS,CAAE,CACTE,eAAe,CAAE5N,KAAK,CAACqN,OAAO,CAACK,IAAI,CAAC,GAAG,CAAC,CACxCC,WAAW,CAAE3N,KAAK,CAACqN,OAAO,CAACK,IAAI,CAAC,GAAG,CACrC,CACF,CAAC,CACH,CAAE,CAAApD,QAAA,CAEDlD,oBAAoB,CAAC3G,eAAe,CAAC,CAChC,CACT,CAEA,CAAAA,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEsG,MAAM,IAAK,WAAW,eACtCvH,IAAA,CAACrD,MAAM,EACLsQ,OAAO,CAAEA,CAAA,GAAM,CACb3L,oBAAoB,CAAC,KAAK,CAAC,CAC3BF,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CACF4J,KAAK,CAAC,OAAO,CACbD,OAAO,CAAC,WAAW,CACnBiC,SAAS,cAAEhN,IAAA,CAACjC,UAAU,GAAE,CAAE,CAAA+M,QAAA,CAEzBzK,CAAC,CAAC,iBAAiB,CAAC,CACf,CACT,EACY,CAAC,EACV,CACT,CAED;AACA,KAAM,CAAAgO,kBAAkB,CAAGA,CAAA,gBACzBnO,KAAA,CAACnD,MAAM,EAACoQ,IAAI,CAAEhM,gBAAiB,CAACiM,OAAO,CAAEA,CAAA,GAAMhM,mBAAmB,CAAC,KAAK,CAAE,CAAA0J,QAAA,eACxE5K,KAAA,CAAClD,WAAW,EAAA8N,QAAA,EACTzK,CAAC,CAAC,wBAAwB,CAAC,cAC5BL,IAAA,CAAC7C,UAAU,EACT,aAAW,OAAO,CAClB8P,OAAO,CAAEA,CAAA,GAAM7L,mBAAmB,CAAC,KAAK,CAAE,CAC1CuJ,EAAE,CAAE,CAAE4C,QAAQ,CAAE,UAAU,CAAEC,KAAK,CAAE,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA3C,QAAA,cAE/C9K,IAAA,CAAC/B,SAAS,GAAE,CAAC,CACH,CAAC,EACF,CAAC,cACdiC,KAAA,CAACjD,aAAa,EAAA6N,QAAA,eACZ9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAAAD,QAAA,CACxBzK,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,CACZY,eAAe,eACdf,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,eACjB5K,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACiD,YAAY,MAAAlD,QAAA,eACtC5K,KAAA,WAAA4K,QAAA,EAASzK,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACY,eAAe,CAACuD,YAAY,EAC7D,CAAC,cACbtE,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACiD,YAAY,MAAAlD,QAAA,eACtC5K,KAAA,WAAA4K,QAAA,EAASzK,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACoI,iBAAiB,CAACxH,eAAe,CAAC6C,QAAQ,CAAC,EACzE,CAAC,cACb5D,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACiD,YAAY,MAAAlD,QAAA,eACtC5K,KAAA,WAAA4K,QAAA,EAASzK,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACuI,iBAAiB,CAAC3H,eAAe,CAAC6C,QAAQ,CAAC,EACzE,CAAC,cACb5D,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACiD,YAAY,MAAChD,KAAK,CAAC,gBAAgB,CAAAF,QAAA,eAC7D5K,KAAA,WAAA4K,QAAA,EAASzK,CAAC,CAAC,oBAAoB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACyI,sBAAsB,CAAC7H,eAAe,CAAC6C,QAAQ,CAAEgG,QAAQ,CAAC7I,eAAe,CAAC+C,QAAQ,CAAC,EAAI,EAAE,CAAC,EAC7H,CAAC,EACV,CACN,EACY,CAAC,cAChB9D,KAAA,CAAChD,aAAa,EAAA4N,QAAA,eACZ9K,IAAA,CAACrD,MAAM,EAACsQ,OAAO,CAAEA,CAAA,GAAM7L,mBAAmB,CAAC,KAAK,CAAE,CAAA0J,QAAA,CAC/CzK,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,cACTL,IAAA,CAACrD,MAAM,EACLsQ,OAAO,CAAEnF,mBAAoB,CAC7BkD,KAAK,CAAC,OAAO,CACbD,OAAO,CAAC,WAAW,CACnBkD,QAAQ,CAAE1M,iBAAkB,CAAAuJ,QAAA,CAE3BvJ,iBAAiB,CAAGlB,CAAC,CAAC,qBAAqB,CAAC,CAAGA,CAAC,CAAC,8BAA8B,CAAC,CAC3E,CAAC,EACI,CAAC,EACV,CACT,CAED,mBACEH,KAAA,CAACb,MAAM,EAAAyL,QAAA,eACL9K,IAAA,CAAC5D,SAAS,EAACiR,QAAQ,CAAC,IAAI,CAAC1C,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cACrC5K,KAAA,CAACP,sBAAsB,EAAC2O,WAAW,CAAE,CAAC,2BAA2B,CAAE,oBAAoB,CAAE,CAAAxD,QAAA,eACvF9K,IAAA,CAACzD,KAAK,EAACgP,SAAS,CAAE,CAAE,CAACZ,EAAE,CAAE,CAAEiB,CAAC,CAAE,CAAC,CAAEI,EAAE,CAAE,CAAC,CAAEL,OAAO,CAAE,cAAc,CAAEX,KAAK,CAAE,OAAQ,CAAE,CAAAF,QAAA,cAChF5K,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEgB,cAAc,CAAE,eAAe,CAAEZ,UAAU,CAAE,QAAQ,CAAE0C,QAAQ,CAAE,MAAM,CAAEzC,GAAG,CAAE,CAAE,CAAE,CAAAhB,QAAA,eAC5G5K,KAAA,CAAC5D,GAAG,EAAAwO,QAAA,eACF9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,IAAI,CAACiD,YAAY,MAACrD,EAAE,CAAE,CAAE+B,UAAU,CAAE,MAAO,CAAE,CAAA5B,QAAA,CAC9DzK,CAAC,CAAC,sBAAsB,CAAC,CAChB,CAAC,cACbL,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACJ,EAAE,CAAE,CAAE6D,OAAO,CAAE,GAAI,CAAE,CAAA1D,QAAA,CAC9CzK,CAAC,CAAC,4BAA4B,CAAC,CACtB,CAAC,EACV,CAAC,cACNH,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAhB,QAAA,eACzD5K,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAAAE,QAAA,eAC9B9K,IAAA,CAAC3D,UAAU,EAAC0O,OAAO,CAAC,OAAO,CAACJ,EAAE,CAAE,CAAE6D,OAAO,CAAE,GAAG,CAAExC,EAAE,CAAE,GAAI,CAAE,CAAAlB,QAAA,CACvDzK,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,cACbH,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,IAAI,CAACJ,EAAE,CAAE,CAAE+B,UAAU,CAAE,MAAO,CAAE,CAAA5B,QAAA,EAAC,eAChD,CAACpM,MAAM,CAACiD,gBAAgB,CAAE,OAAO,CAAE,CAAE+G,MAAM,CAAEjI,KAAK,CAAG1B,EAAE,CAAGC,IAAK,CAAC,CAAC,CAAC,KAAG,CAACN,MAAM,CAACC,OAAO,CAACgD,gBAAgB,CAAE,CAAC,CAAC,CAAE,aAAa,CAAE,CAAE+G,MAAM,CAAEjI,KAAK,CAAG1B,EAAE,CAAGC,IAAK,CAAC,CAAC,EACjJ,CAAC,cACbkB,KAAA,CAAC7D,UAAU,EAAC0O,OAAO,CAAC,SAAS,CAACJ,EAAE,CAAE,CAAE6D,OAAO,CAAE,GAAI,CAAE,CAAA1D,QAAA,EAChDrJ,cAAc,SAAdA,cAAc,WAAdA,cAAc,CAAE2F,QAAQ,CACvB1H,MAAM,CAACF,2BAA2B,CAAC,GAAI,CAAAsC,IAAI,CAAC,CAAC,CAAC2M,WAAW,CAAC,CAAC,CAAEhN,cAAc,CAAC2F,QAAQ,CAAE,qBAAqB,CAAC,CAAE,qBAAqB,CAAC,CAAC1I,MAAM,CAAC,QAAQ,CAAC,CAErJA,MAAM,CAAC0D,WAAW,CAAE,GAAG,CAAE,CACvBsG,MAAM,CAAEjI,KAAK,CAAG1B,EAAE,CAAGC,IACvB,CAAC,CACF,CACA,CAAAyC,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE2F,QAAQ,GAAI,KAAK3F,cAAc,CAAC2F,QAAQ,GAAG,EAClD,CAAC,EACV,CAAC,cACNlH,KAAA,CAAC5D,GAAG,EAACqO,EAAE,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEK,GAAG,CAAE,CAAE,CAAE,CAAAhB,QAAA,eACnC9K,IAAA,CAACzC,OAAO,EAACmR,KAAK,CAAErO,CAAC,CAAC,sBAAsB,CAAE,CAAAyK,QAAA,cACxC9K,IAAA,SAAA8K,QAAA,cACE9K,IAAA,CAAC7C,UAAU,EACT8P,OAAO,CAAEtK,gBAAiB,CAC1BsL,QAAQ,CAAEhL,sBAAsB,CAAC,CAAE,CACnC0H,EAAE,CAAE,CACFK,KAAK,CAAE,OAAO,CACdW,OAAO,CAAE,0BAA0B,CACnC,SAAS,CAAE,CACTA,OAAO,CAAE,0BACX,CAAC,CACD,YAAY,CAAE,CACZX,KAAK,CAAE,0BAA0B,CACjCW,OAAO,CAAE,2BACX,CACF,CAAE,CAAAb,QAAA,cAEF9K,IAAA,CAACzB,eAAe,GAAE,CAAC,CACT,CAAC,CACT,CAAC,CACA,CAAC,cACVyB,IAAA,CAACzC,OAAO,EAACmR,KAAK,CAAErO,CAAC,CAAC,kBAAkB,CAAE,CAAAyK,QAAA,cACpC9K,IAAA,SAAA8K,QAAA,cACE9K,IAAA,CAAC7C,UAAU,EACT8P,OAAO,CAAEpK,YAAa,CACtBoL,QAAQ,CAAE/K,kBAAkB,CAAC,CAAE,CAC/ByH,EAAE,CAAE,CACFK,KAAK,CAAE,OAAO,CACdW,OAAO,CAAE,0BAA0B,CACnC,SAAS,CAAE,CACTA,OAAO,CAAE,0BACX,CAAC,CACD,YAAY,CAAE,CACZX,KAAK,CAAE,0BAA0B,CACjCW,OAAO,CAAE,2BACX,CACF,CAAE,CAAAb,QAAA,cAEF9K,IAAA,CAACvB,gBAAgB,GAAE,CAAC,CACV,CAAC,CACT,CAAC,CACA,CAAC,EACP,CAAC,EACH,CAAC,EACH,CAAC,CACD,CAAC,CAEToC,OAAO,cACNb,IAAA,CAAC1D,GAAG,EAACqO,EAAE,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEgB,cAAc,CAAE,QAAQ,CAAE5B,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cAC5D9K,IAAA,CAACnD,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACJkE,KAAK,cACPf,IAAA,CAAClD,KAAK,EAAC6R,QAAQ,CAAC,OAAO,CAAChE,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,CACnC/J,KAAK,CACD,CAAC,cAERf,IAAA,CAACV,mBAAmB,EAClBqB,QAAQ,CAAEA,QAAS,CACnBE,OAAO,CAAEA,OAAQ,CACjBc,gBAAgB,CAAEA,gBAAiB,CACnCe,UAAU,CAAEA,UAAW,CACvBkM,aAAa,CAAExI,iBAAkB,CACjCyI,eAAe,CAAExI,wBAAyB,CAC1C5E,cAAc,CAAEA,cAAe,CAC/BmH,iBAAiB,CAAEA,iBAAkB,CACrCT,cAAc,CAAEA,cAAe,CAChC,CACF,CAEA+E,mBAAmB,CAAC,CAAC,CACrBmB,kBAAkB,CAAC,CAAC,EACG,CAAC,CAChB,CAAC,cAGZrO,IAAA,CAACjD,MAAM,EACL+R,UAAU,MACV3B,IAAI,CAAEnL,WAAY,CAClBoL,OAAO,CAAE1G,kBAAmB,CAAAoE,QAAA,CAE3B5I,cAAc,eACblC,IAAA,CAACJ,eAAe,EACdmP,MAAM,CAAE7M,cAAc,CAACqE,SAAU,CACjCgE,SAAS,CAAErI,cAAc,CAACsE,UAAW,CACrCwI,WAAW,CAAE,CACX,GAAG9M,cAAc,CACjB+M,YAAY,CAAE/M,cAAc,CAAC4B,QAAQ,CACrCE,QAAQ,CAAE8F,QAAQ,CAAC5H,cAAc,CAAC8B,QAAQ,CAAC,EAAI,EACjD,CAAE,CACFoJ,OAAO,CAAE1G,kBAAmB,CAC7B,CACF,CACK,CAAC,cAGT1G,IAAA,CAACH,qBAAqB,EACpBsN,IAAI,CAAE7K,kBAAmB,CACzB4M,OAAO,CAAE1M,eAAgB,CACzB4E,QAAQ,CAAE,CAAA3F,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE2F,QAAQ,GAAI,IAAK,CAC3C+H,QAAQ,CAAE7E,oBAAqB,CAC/B8C,OAAO,CAAEA,CAAA,GAAM,CACb7K,qBAAqB,CAAC,KAAK,CAAC,CAC5BE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACH,CAAC,EACI,CAAC,CAEb,CAAC,CAED,cAAe,CAAArC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}