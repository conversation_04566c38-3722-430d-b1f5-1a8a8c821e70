{"version": 3, "file": "static/js/reactPlayerYouTube.ceda7deb.chunk.js", "mappings": "kFAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAkB,CAAC,EAzBRC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAiB,CACxBK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,OAC/BC,EAAeD,EAAQ,MACvBE,EAAkBF,EAAQ,MAC9B,MAGMG,EAAiB,wCACjBC,EAAqB,4BACrBC,EAAiB,wBAEvB,MAAMZ,UAAgBG,EAAaU,UACjCC,WAAAA,GACEC,SAASC,WACT5B,EAAc6B,KAAM,aAAcT,EAAaU,YAC/C9B,EAAc6B,KAAM,iBAAkBE,IACpC,GAAIA,aAAeC,MACjB,MAAO,CACLC,SAAU,WACVC,SAAUH,EAAII,IAAIN,KAAKO,OAAOC,KAAK,MAGvC,GAAIf,EAAegB,KAAKP,GAAM,CAC5B,MAAO,CAAEQ,GAAcR,EAAIS,MAAMlB,GACjC,MAAO,CACLW,SAAU,WACVQ,KAAMF,EAAWG,QAAQ,MAAO,MAEpC,CACA,GAAInB,EAAmBe,KAAKP,GAAM,CAChC,MAAO,CAAEY,GAAYZ,EAAIS,MAAMjB,GAC/B,MAAO,CACLU,SAAU,eACVQ,KAAME,EAEV,CACA,MAAO,CAAC,CAAC,IAEX3C,EAAc6B,KAAM,iBAAkBe,IACpC,MAAM,KAAEC,GAASD,GACX,OAAEE,EAAM,QAAEC,EAAO,SAAEC,EAAQ,YAAEC,EAAW,QAAEC,EAAO,QAAEC,EAAO,KAAEC,EAAMC,QAAQ,WAAEC,EAAU,YAAEC,IAAkB1B,KAAK2B,OAC/G,UAAEC,EAAS,QAAEC,EAAO,OAAEC,EAAM,UAAEC,EAAS,MAAEC,EAAK,KAAEC,GAASC,OAAiB,GAAEC,YAWlF,GAVInB,IAASY,GACXF,IACEV,IAASa,IACXZ,IACAG,KAEEJ,IAASc,GACXZ,IACEF,IAASe,GACXZ,IACEH,IAASgB,EAAO,CAClB,MAAMI,IAAepC,KAAKC,WAAW,eACjCsB,IAASa,IACPX,EAAWY,MACbrC,KAAKsC,OAAOb,EAAWY,OAEvBrC,KAAKuC,QAGTlB,GACF,CACIL,IAASiB,GACXX,GAAS,IAEbnD,EAAc6B,KAAM,QAAQ,KAC1BA,KAAKC,WAAW,OAAO,IAEzB9B,EAAc6B,KAAM,UAAU,KAC5BA,KAAKC,WAAW,SAAS,IAE3B9B,EAAc6B,KAAM,OAAQwC,IAC1BxC,KAAKwC,UAAYA,CAAS,GAE9B,CACAC,iBAAAA,GACEzC,KAAK2B,MAAMe,SAAW1C,KAAK2B,MAAMe,QAAQ1C,KAC3C,CACAO,KAAAA,CAAML,GACJ,OAAKA,GAAOA,aAAeC,OAASV,EAAegB,KAAKP,GAC/C,KAEFA,EAAIS,MAAMnB,EAAgBmD,mBAAmB,EACtD,CACAC,IAAAA,CAAK1C,EAAK2C,GACR,MAAM,QAAEC,EAAO,MAAEC,EAAK,YAAEC,EAAW,SAAEC,EAAQ,KAAE1B,EAAI,OAAEC,EAAM,QAAE0B,GAAYlD,KAAK2B,OACxE,WAAEF,EAAU,aAAE0B,GAAiB3B,EAC/B4B,EAAKpD,KAAKO,MAAML,GACtB,GAAI2C,EACF,OAAIpD,EAAegB,KAAKP,IAAQR,EAAmBe,KAAKP,IAAQA,aAAeC,WAC7EH,KAAKqD,OAAOC,aAAatD,KAAKuD,cAAcrD,SAG9CF,KAAKqD,OAAOG,aAAa,CACvBC,QAASL,EACTM,cAAc,EAAInE,EAAaoE,gBAAgBzD,IAAQuB,EAAWY,MAClEuB,YAAY,EAAIrE,EAAasE,cAAc3D,IAAQuB,EAAWqC,OAIlE,EAAIvE,EAAawE,QAjGL,qCACG,KACM,2BA+F4CC,GAAOA,EAAGC,SAAQC,MAAMF,IAClFhE,KAAKwC,YAEVxC,KAAKqD,OAAS,IAAIW,EAAGG,OAAOnE,KAAKwC,UAAW,CAC1C4B,MAAO,OACPC,OAAQ,OACRZ,QAASL,EACT3B,WAAY,CACV6C,SAAUxB,EAAU,EAAI,EACxByB,KAAMxB,EAAQ,EAAI,EAClBE,SAAUA,EAAW,EAAI,EACzBZ,OAAO,EAAI9C,EAAaoE,gBAAgBzD,GACxC4D,KAAK,EAAIvE,EAAasE,cAAc3D,GACpCsE,OAAQtC,OAAOuC,SAASD,OACxBxB,YAAaA,EAAc,EAAI,KAC5BhD,KAAKuD,cAAcrD,MACnBuB,GAELiD,OAAQ,CACNpD,QAASA,KACHC,GACFvB,KAAKqD,OAAOsB,SAAQ,GAEtB3E,KAAK2B,MAAML,SAAS,EAEtBsD,qBAAuB7D,GAAUf,KAAK2B,MAAMiD,qBAAqB7D,EAAMC,MACvE6D,wBAA0B9D,GAAUf,KAAK2B,MAAMkD,wBAAwB9D,GACvE+D,cAAe9E,KAAK8E,cACpB5B,QAAUnC,GAAUmC,EAAQnC,EAAMC,OAEpC+D,KAAMpF,EAAec,KAAKP,GAzHZ,wCAyHmC,KAC9CiD,IACH,GACDD,GACCC,EAAauB,QACfM,QAAQC,KAAK,mIAEjB,CACA1C,IAAAA,GACEvC,KAAKC,WAAW,YAClB,CACAiF,KAAAA,GACElF,KAAKC,WAAW,aAClB,CACAkF,IAAAA,GACOC,SAASC,KAAKC,SAAStF,KAAKC,WAAW,eAE5CD,KAAKC,WAAW,YAClB,CACAqC,MAAAA,CAAOiD,GAA6B,IAArBC,EAAWzF,UAAA0F,OAAA,QAAAC,IAAA3F,UAAA,IAAAA,UAAA,GACxBC,KAAKC,WAAW,SAAUsF,GACrBC,GAAgBxF,KAAK2B,MAAMmB,SAC9B9C,KAAKkF,OAET,CACAS,SAAAA,CAAUC,GACR5F,KAAKC,WAAW,YAAwB,IAAX2F,EAC/B,CACAC,eAAAA,CAAgBC,GACd9F,KAAKC,WAAW,kBAAmB6F,EACrC,CACAnB,OAAAA,CAAQpD,GACNvB,KAAKC,WAAW,UAAWsB,EAC7B,CACAwE,WAAAA,GACE,OAAO/F,KAAKC,WAAW,cACzB,CACA+F,cAAAA,GACE,OAAOhG,KAAKC,WAAW,iBACzB,CACAgG,gBAAAA,GACE,OAAOjG,KAAKC,WAAW,0BAA4BD,KAAK+F,aAC1D,CACAG,MAAAA,GACE,MAAM,QAAEC,GAAYnG,KAAK2B,MACnByE,EAAQ,CACZhC,MAAO,OACPC,OAAQ,OACR8B,WAEF,OAAuBjH,EAAaJ,QAAQuH,cAAc,MAAO,CAAED,SAAyBlH,EAAaJ,QAAQuH,cAAc,MAAO,CAAEC,IAAKtG,KAAKsG,MACpJ,EAEFnI,EAAcY,EAAS,cAAe,WACtCZ,EAAcY,EAAS,UAAWS,EAAgB+G,QAAQC,Q", "sources": ["../node_modules/react-player/lib/players/YouTube.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar YouTube_exports = {};\n__export(YouTube_exports, {\n  default: () => YouTube\n});\nmodule.exports = __toCommonJS(YouTube_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://www.youtube.com/iframe_api\";\nconst SDK_GLOBAL = \"YT\";\nconst SDK_GLOBAL_READY = \"onYouTubeIframeAPIReady\";\nconst MATCH_PLAYLIST = /[?&](?:list|channel)=([a-zA-Z0-9_-]+)/;\nconst MATCH_USER_UPLOADS = /user\\/([a-zA-Z0-9_-]+)\\/?/;\nconst MATCH_NOCOOKIE = /youtube-nocookie\\.com/;\nconst NOCOOKIE_HOST = \"https://www.youtube-nocookie.com\";\nclass YouTube extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"parsePlaylist\", (url) => {\n      if (url instanceof Array) {\n        return {\n          listType: \"playlist\",\n          playlist: url.map(this.getID).join(\",\")\n        };\n      }\n      if (MATCH_PLAYLIST.test(url)) {\n        const [, playlistId] = url.match(MATCH_PLAYLIST);\n        return {\n          listType: \"playlist\",\n          list: playlistId.replace(/^UC/, \"UU\")\n        };\n      }\n      if (MATCH_USER_UPLOADS.test(url)) {\n        const [, username] = url.match(MATCH_USER_UPLOADS);\n        return {\n          listType: \"user_uploads\",\n          list: username\n        };\n      }\n      return {};\n    });\n    __publicField(this, \"onStateChange\", (event) => {\n      const { data } = event;\n      const { onPlay, onPause, onBuffer, onBufferEnd, onEnded, onReady, loop, config: { playerVars, onUnstarted } } = this.props;\n      const { UNSTARTED, PLAYING, PAUSED, BUFFERING, ENDED, CUED } = window[SDK_GLOBAL].PlayerState;\n      if (data === UNSTARTED)\n        onUnstarted();\n      if (data === PLAYING) {\n        onPlay();\n        onBufferEnd();\n      }\n      if (data === PAUSED)\n        onPause();\n      if (data === BUFFERING)\n        onBuffer();\n      if (data === ENDED) {\n        const isPlaylist = !!this.callPlayer(\"getPlaylist\");\n        if (loop && !isPlaylist) {\n          if (playerVars.start) {\n            this.seekTo(playerVars.start);\n          } else {\n            this.play();\n          }\n        }\n        onEnded();\n      }\n      if (data === CUED)\n        onReady();\n    });\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unMute\");\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  getID(url) {\n    if (!url || url instanceof Array || MATCH_PLAYLIST.test(url)) {\n      return null;\n    }\n    return url.match(import_patterns.MATCH_URL_YOUTUBE)[1];\n  }\n  load(url, isReady) {\n    const { playing, muted, playsinline, controls, loop, config, onError } = this.props;\n    const { playerVars, embedOptions } = config;\n    const id = this.getID(url);\n    if (isReady) {\n      if (MATCH_PLAYLIST.test(url) || MATCH_USER_UPLOADS.test(url) || url instanceof Array) {\n        this.player.loadPlaylist(this.parsePlaylist(url));\n        return;\n      }\n      this.player.cueVideoById({\n        videoId: id,\n        startSeconds: (0, import_utils.parseStartTime)(url) || playerVars.start,\n        endSeconds: (0, import_utils.parseEndTime)(url) || playerVars.end\n      });\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY, (YT) => YT.loaded).then((YT) => {\n      if (!this.container)\n        return;\n      this.player = new YT.Player(this.container, {\n        width: \"100%\",\n        height: \"100%\",\n        videoId: id,\n        playerVars: {\n          autoplay: playing ? 1 : 0,\n          mute: muted ? 1 : 0,\n          controls: controls ? 1 : 0,\n          start: (0, import_utils.parseStartTime)(url),\n          end: (0, import_utils.parseEndTime)(url),\n          origin: window.location.origin,\n          playsinline: playsinline ? 1 : 0,\n          ...this.parsePlaylist(url),\n          ...playerVars\n        },\n        events: {\n          onReady: () => {\n            if (loop) {\n              this.player.setLoop(true);\n            }\n            this.props.onReady();\n          },\n          onPlaybackRateChange: (event) => this.props.onPlaybackRateChange(event.data),\n          onPlaybackQualityChange: (event) => this.props.onPlaybackQualityChange(event),\n          onStateChange: this.onStateChange,\n          onError: (event) => onError(event.data)\n        },\n        host: MATCH_NOCOOKIE.test(url) ? NOCOOKIE_HOST : void 0,\n        ...embedOptions\n      });\n    }, onError);\n    if (embedOptions.events) {\n      console.warn(\"Using `embedOptions.events` will likely break things. Use ReactPlayer\\u2019s callback props instead, eg onReady, onPlay, onPause\");\n    }\n  }\n  play() {\n    this.callPlayer(\"playVideo\");\n  }\n  pause() {\n    this.callPlayer(\"pauseVideo\");\n  }\n  stop() {\n    if (!document.body.contains(this.callPlayer(\"getIframe\")))\n      return;\n    this.callPlayer(\"stopVideo\");\n  }\n  seekTo(amount, keepPlaying = false) {\n    this.callPlayer(\"seekTo\", amount);\n    if (!keepPlaying && !this.props.playing) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction * 100);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"setPlaybackRate\", rate);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  getDuration() {\n    return this.callPlayer(\"getDuration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"getCurrentTime\");\n  }\n  getSecondsLoaded() {\n    return this.callPlayer(\"getVideoLoadedFraction\") * this.getDuration();\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style }, /* @__PURE__ */ import_react.default.createElement(\"div\", { ref: this.ref }));\n  }\n}\n__publicField(YouTube, \"displayName\", \"YouTube\");\n__publicField(YouTube, \"canPlay\", import_patterns.canPlay.youtube);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "YouTube_exports", "__export", "target", "all", "name", "default", "YouTube", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "MATCH_PLAYLIST", "MATCH_USER_UPLOADS", "MATCH_NOCOOKIE", "Component", "constructor", "super", "arguments", "this", "callPlayer", "url", "Array", "listType", "playlist", "map", "getID", "join", "test", "playlistId", "match", "list", "replace", "username", "event", "data", "onPlay", "onPause", "onBuffer", "onBufferEnd", "onEnded", "onReady", "loop", "config", "playerVars", "onUnstarted", "props", "UNSTARTED", "PLAYING", "PAUSED", "BUFFERING", "ENDED", "CUED", "window", "PlayerState", "isPlaylist", "start", "seekTo", "play", "container", "componentDidMount", "onMount", "MATCH_URL_YOUTUBE", "load", "isReady", "playing", "muted", "playsinline", "controls", "onError", "embedOptions", "id", "player", "loadPlaylist", "parsePlaylist", "cueVideoById", "videoId", "startSeconds", "parseStartTime", "endSeconds", "parseEndTime", "end", "getSDK", "YT", "loaded", "then", "Player", "width", "height", "autoplay", "mute", "origin", "location", "events", "setLoop", "onPlaybackRateChange", "onPlaybackQualityChange", "onStateChange", "host", "console", "warn", "pause", "stop", "document", "body", "contains", "amount", "keepPlaying", "length", "undefined", "setVolume", "fraction", "setPlaybackRate", "rate", "getDuration", "getCurrentTime", "getSecondsLoaded", "render", "display", "style", "createElement", "ref", "canPlay", "youtube"], "sourceRoot": ""}