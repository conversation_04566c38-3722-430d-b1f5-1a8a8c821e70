{"version": 3, "file": "static/js/reactPlayerKaltura.0c2497c8.chunk.js", "mappings": "kFAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAkB,CAAC,EAzBRC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAiB,CACxBK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,OAC/BC,EAAeD,EAAQ,MACvBE,EAAkBF,EAAQ,MAG9B,MAAMP,UAAgBG,EAAaO,UACjCC,WAAAA,GACEC,SAASC,WACTzB,EAAc0B,KAAM,aAAcN,EAAaO,YAC/C3B,EAAc0B,KAAM,WAAY,MAChC1B,EAAc0B,KAAM,cAAe,MACnC1B,EAAc0B,KAAM,gBAAiB,MACrC1B,EAAc0B,KAAM,QAAQ,KAC1BA,KAAKC,WAAW,OAAO,IAEzB3B,EAAc0B,KAAM,UAAU,KAC5BA,KAAKC,WAAW,SAAS,IAE3B3B,EAAc0B,KAAM,OAAQE,IAC1BF,KAAKE,OAASA,CAAM,GAExB,CACAC,iBAAAA,GACEH,KAAKI,MAAMC,SAAWL,KAAKI,MAAMC,QAAQL,KAC3C,CACAM,IAAAA,CAAKC,IACH,EAAIb,EAAac,QAvBL,2CACG,YAsB+BC,MAAMC,IAC7CV,KAAKE,SAEVF,KAAKW,OAAS,IAAID,EAASE,OAAOZ,KAAKE,QACvCF,KAAKW,OAAOE,GAAG,SAAS,KACtBC,YAAW,KACTd,KAAKW,OAAOI,SAAU,EACtBf,KAAKW,OAAOK,QAAQhB,KAAKI,MAAMa,MAC3BjB,KAAKI,MAAMc,OACblB,KAAKW,OAAOQ,OAEdnB,KAAKoB,aAAapB,KAAKW,OAAQX,KAAKI,OACpCJ,KAAKI,MAAMiB,SAAS,GACnB,IAAI,IACP,GACDrB,KAAKI,MAAMkB,QAChB,CACAF,YAAAA,CAAaT,EAAQP,GACnBO,EAAOE,GAAG,OAAQT,EAAMmB,QACxBZ,EAAOE,GAAG,QAAST,EAAMoB,SACzBb,EAAOE,GAAG,QAAST,EAAMqB,SACzBd,EAAOE,GAAG,QAAST,EAAMkB,SACzBX,EAAOE,GAAG,cAAca,IAA2B,IAA1B,SAAEC,EAAQ,QAAEC,GAASF,EAC5C1B,KAAK2B,SAAWA,EAChB3B,KAAK6B,YAAcD,CAAO,GAE9B,CACAE,IAAAA,GACE9B,KAAKC,WAAW,OAClB,CACA8B,KAAAA,GACE/B,KAAKC,WAAW,QAClB,CACA+B,IAAAA,GACA,CACAC,MAAAA,CAAOL,GAA6B,IAApBM,IAAWnC,UAAAoC,OAAA,QAAAC,IAAArC,UAAA,KAAAA,UAAA,GACzBC,KAAKC,WAAW,iBAAkB2B,GAC7BM,GACHlC,KAAK+B,OAET,CACAM,SAAAA,CAAUC,GACRtC,KAAKC,WAAW,YAAaqC,EAC/B,CACAtB,OAAAA,CAAQC,GACNjB,KAAKC,WAAW,UAAWgB,EAC7B,CACAsB,WAAAA,GACE,OAAOvC,KAAK2B,QACd,CACAa,cAAAA,GACE,OAAOxC,KAAK6B,WACd,CACAY,gBAAAA,GACE,OAAOzC,KAAK0C,aACd,CACAC,MAAAA,GAKE,OAAuBtD,EAAaJ,QAAQ2D,cAC1C,SACA,CACEC,IAAK7C,KAAK6C,IACVC,IAAK9C,KAAKI,MAAMG,IAChBwC,YAAa,IACbC,UAAW,KACXC,MAXU,CACZC,MAAO,OACPC,OAAQ,QAUNC,MAAO,yCACPC,eAAgB,8BAGtB,EAEF/E,EAAcY,EAAS,cAAe,WACtCZ,EAAcY,EAAS,UAAWS,EAAgB2D,QAAQC,Q", "sources": ["../node_modules/react-player/lib/players/Kaltura.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Kaltura_exports = {};\n__export(Kaltura_exports, {\n  default: () => Kaltura\n});\nmodule.exports = __toCommonJS(Kaltura_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://cdn.embed.ly/player-0.1.0.min.js\";\nconst SDK_GLOBAL = \"playerjs\";\nclass Kaltura extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((playerjs) => {\n      if (!this.iframe)\n        return;\n      this.player = new playerjs.Player(this.iframe);\n      this.player.on(\"ready\", () => {\n        setTimeout(() => {\n          this.player.isReady = true;\n          this.player.setLoop(this.props.loop);\n          if (this.props.muted) {\n            this.player.mute();\n          }\n          this.addListeners(this.player, this.props);\n          this.props.onReady();\n        }, 500);\n      });\n    }, this.props.onError);\n  }\n  addListeners(player, props) {\n    player.on(\"play\", props.onPlay);\n    player.on(\"pause\", props.onPause);\n    player.on(\"ended\", props.onEnded);\n    player.on(\"error\", props.onError);\n    player.on(\"timeupdate\", ({ duration, seconds }) => {\n      this.duration = duration;\n      this.currentTime = seconds;\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"setCurrentTime\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.secondsLoaded;\n  }\n  render() {\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        ref: this.ref,\n        src: this.props.url,\n        frameBorder: \"0\",\n        scrolling: \"no\",\n        style,\n        allow: \"encrypted-media; autoplay; fullscreen;\",\n        referrerPolicy: \"no-referrer-when-downgrade\"\n      }\n    );\n  }\n}\n__publicField(Kaltura, \"displayName\", \"Kaltura\");\n__publicField(Kaltura, \"canPlay\", import_patterns.canPlay.kaltura);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "Kaltura_exports", "__export", "target", "all", "name", "default", "<PERSON><PERSON><PERSON>", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "iframe", "componentDidMount", "props", "onMount", "load", "url", "getSDK", "then", "playerjs", "player", "Player", "on", "setTimeout", "isReady", "setLoop", "loop", "muted", "mute", "addListeners", "onReady", "onError", "onPlay", "onPause", "onEnded", "_ref", "duration", "seconds", "currentTime", "play", "pause", "stop", "seekTo", "keepPlaying", "length", "undefined", "setVolume", "fraction", "getDuration", "getCurrentTime", "getSecondsLoaded", "secondsLoaded", "render", "createElement", "ref", "src", "frameBorder", "scrolling", "style", "width", "height", "allow", "referrerPolicy", "canPlay", "kaltura"], "sourceRoot": ""}