{"version": 3, "file": "static/js/reactPlayerPreview.53c1bf01.chunk.js", "mappings": "iFAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAkB,CAAC,EAzBRC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAiB,CACxBK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,OACnC,MAAMC,EAAY,OACZC,EAAQ,CAAC,EACf,MAAMT,UAAgBG,EAAaO,UACjCC,WAAAA,GACEC,SAASC,WACTzB,EAAc0B,KAAM,WAAW,GAC/B1B,EAAc0B,KAAM,QAAS,CAC3BC,MAAO,OAET3B,EAAc0B,KAAM,kBAAmBE,IACvB,UAAVA,EAAEhC,KAA6B,MAAVgC,EAAEhC,KACzB8B,KAAKG,MAAMC,SACb,GAEJ,CACAC,iBAAAA,GACEL,KAAKM,SAAU,EACfN,KAAKO,WAAWP,KAAKG,MACvB,CACAK,kBAAAA,CAAmBC,GACjB,MAAM,IAAEC,EAAG,MAAEC,GAAUX,KAAKG,MACxBM,EAAUC,MAAQA,GAAOD,EAAUE,QAAUA,GAC/CX,KAAKO,WAAWP,KAAKG,MAEzB,CACAS,oBAAAA,GACEZ,KAAKM,SAAU,CACjB,CACAC,UAAAA,CAAUM,GAA4B,IAA3B,IAAEH,EAAG,MAAEC,EAAK,UAAEG,GAAWD,EAClC,IAAIxB,EAAaJ,QAAQ8B,eAAeJ,GAGxC,GAAqB,kBAAVA,EAAX,CAIA,IAAIhB,EAAMe,GAKV,OADAV,KAAKgB,SAAS,CAAEf,MAAO,OAChBgB,OAAOC,MAAMJ,EAAUK,QAAQ,QAAST,IAAMU,MAAMC,GAAaA,EAASC,SAAQF,MAAMG,IAC7F,GAAIA,EAAKC,eAAiBxB,KAAKM,QAAS,CACtC,MAAML,EAAQsB,EAAKC,cAAcL,QAAQ,aAAc,cAAcA,QAAQ,aAAc,UAC3FnB,KAAKgB,SAAS,CAAEf,UAChBN,EAAMe,GAAOT,CACf,KATAD,KAAKgB,SAAS,CAAEf,MAAON,EAAMe,IAF/B,MAFEV,KAAKgB,SAAS,CAAEf,MAAOU,GAe3B,CACAc,MAAAA,GACE,MAAM,MAAEd,EAAK,QAAEP,EAAO,SAAEsB,EAAQ,gBAAEC,EAAe,iBAAEC,GAAqB5B,KAAKG,OACvE,MAAEF,GAAUD,KAAK6B,MACjBC,EAAYzC,EAAaJ,QAAQ8B,eAAeJ,GAChDoB,EAAa,CACjBC,QAAS,OACTC,WAAY,SACZC,eAAgB,UAEZC,EAAS,CACbC,QAAS,CACPC,MAAO,OACPC,OAAQ,OACRC,gBAAiBtC,IAAU6B,EAAY,OAAO7B,UAAW,EACzDuC,eAAgB,QAChBC,mBAAoB,SACpBC,OAAQ,aACLX,GAELY,OAAQ,CACNC,WAAY,2DACZC,aAAcnD,EACd2C,MAAO3C,EACP4C,OAAQ5C,EACRoD,SAAUhB,EAAY,gBAAa,KAChCC,GAELL,SAAU,CACRqB,YAAa,QACbC,YAAa,mBACbC,YAAa,4CACbC,WAAY,QAGVC,EAAkC9D,EAAaJ,QAAQmE,cAAc,MAAO,CAAEC,MAAOlB,EAAOQ,OAAQW,UAAW,wBAA0CjE,EAAaJ,QAAQmE,cAAc,MAAO,CAAEC,MAAOlB,EAAOT,SAAU4B,UAAW,6BAC9O,OAAuBjE,EAAaJ,QAAQmE,cAC1C,MACA,CACEC,MAAOlB,EAAOC,QACdkB,UAAW,wBACXlD,UACAmD,SAAU5B,EACV6B,WAAYxD,KAAKyD,kBACd7B,EAAmB,CAAE,aAAcA,GAAqB,CAAC,GAE9DE,EAAYnB,EAAQ,KACpBe,GAAYyB,EAEhB,E", "sources": ["../node_modules/react-player/lib/Preview.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Preview_exports = {};\n__export(Preview_exports, {\n  default: () => Preview\n});\nmodule.exports = __toCommonJS(Preview_exports);\nvar import_react = __toESM(require(\"react\"));\nconst ICON_SIZE = \"64px\";\nconst cache = {};\nclass Preview extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"mounted\", false);\n    __publicField(this, \"state\", {\n      image: null\n    });\n    __publicField(this, \"handleKeyPress\", (e) => {\n      if (e.key === \"Enter\" || e.key === \" \") {\n        this.props.onClick();\n      }\n    });\n  }\n  componentDidMount() {\n    this.mounted = true;\n    this.fetchImage(this.props);\n  }\n  componentDidUpdate(prevProps) {\n    const { url, light } = this.props;\n    if (prevProps.url !== url || prevProps.light !== light) {\n      this.fetchImage(this.props);\n    }\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n  }\n  fetchImage({ url, light, oEmbedUrl }) {\n    if (import_react.default.isValidElement(light)) {\n      return;\n    }\n    if (typeof light === \"string\") {\n      this.setState({ image: light });\n      return;\n    }\n    if (cache[url]) {\n      this.setState({ image: cache[url] });\n      return;\n    }\n    this.setState({ image: null });\n    return window.fetch(oEmbedUrl.replace(\"{url}\", url)).then((response) => response.json()).then((data) => {\n      if (data.thumbnail_url && this.mounted) {\n        const image = data.thumbnail_url.replace(\"height=100\", \"height=480\").replace(\"-d_295x166\", \"-d_640\");\n        this.setState({ image });\n        cache[url] = image;\n      }\n    });\n  }\n  render() {\n    const { light, onClick, playIcon, previewTabIndex, previewAriaLabel } = this.props;\n    const { image } = this.state;\n    const isElement = import_react.default.isValidElement(light);\n    const flexCenter = {\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\"\n    };\n    const styles = {\n      preview: {\n        width: \"100%\",\n        height: \"100%\",\n        backgroundImage: image && !isElement ? `url(${image})` : void 0,\n        backgroundSize: \"cover\",\n        backgroundPosition: \"center\",\n        cursor: \"pointer\",\n        ...flexCenter\n      },\n      shadow: {\n        background: \"radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)\",\n        borderRadius: ICON_SIZE,\n        width: ICON_SIZE,\n        height: ICON_SIZE,\n        position: isElement ? \"absolute\" : void 0,\n        ...flexCenter\n      },\n      playIcon: {\n        borderStyle: \"solid\",\n        borderWidth: \"16px 0 16px 26px\",\n        borderColor: \"transparent transparent transparent white\",\n        marginLeft: \"7px\"\n      }\n    };\n    const defaultPlayIcon = /* @__PURE__ */ import_react.default.createElement(\"div\", { style: styles.shadow, className: \"react-player__shadow\" }, /* @__PURE__ */ import_react.default.createElement(\"div\", { style: styles.playIcon, className: \"react-player__play-icon\" }));\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        style: styles.preview,\n        className: \"react-player__preview\",\n        onClick,\n        tabIndex: previewTabIndex,\n        onKeyPress: this.handleKeyPress,\n        ...previewAriaLabel ? { \"aria-label\": previewAriaLabel } : {}\n      },\n      isElement ? light : null,\n      playIcon || defaultPlayIcon\n    );\n  }\n}\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "Preview_exports", "__export", "target", "all", "name", "default", "Preview", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "ICON_SIZE", "cache", "Component", "constructor", "super", "arguments", "this", "image", "e", "props", "onClick", "componentDidMount", "mounted", "fetchImage", "componentDidUpdate", "prevProps", "url", "light", "componentWillUnmount", "_ref", "oEmbedUrl", "isValidElement", "setState", "window", "fetch", "replace", "then", "response", "json", "data", "thumbnail_url", "render", "playIcon", "previewTabIndex", "previewAriaLabel", "state", "isElement", "flexCenter", "display", "alignItems", "justifyContent", "styles", "preview", "width", "height", "backgroundImage", "backgroundSize", "backgroundPosition", "cursor", "shadow", "background", "borderRadius", "position", "borderStyle", "borderWidth", "borderColor", "marginLeft", "defaultPlayIcon", "createElement", "style", "className", "tabIndex", "onKeyPress", "handleKeyPress"], "sourceRoot": ""}