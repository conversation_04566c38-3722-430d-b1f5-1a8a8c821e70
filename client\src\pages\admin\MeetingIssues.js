import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import i18n from 'i18next';
import enBundle from '../../i18n/adminMeetingIssues.en.json';
import arBundle from '../../i18n/adminMeetingIssues.ar.json';
import {
  Container, 
  Typography, 
  Paper, 
  Table, 
  TableHead, 
  TableRow, 
  TableCell, 
  TableBody, 
  Chip, 
  CircularProgress,
  Box,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Divider,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Info as InfoIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  VideoCall as VideoCallIcon,
  Visibility as VisibilityIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';

const IssueChip = ({ type, t }) => {
  const getChipConfig = (type) => {
    switch (type) {
      case 'teacher_absent':
        return { color: 'warning', label: t('type.teacher_absent', 'Teacher Absent') };
      case 'technical_issue':
        return { color: 'error', label: t('type.technical_issue', 'Technical Issue') };
      case 'pending':
        return { color: 'info', label: t('type.pending', 'Pending') };
      case 'no_issue':
        return { color: 'success', label: t('type.no_issue', 'No Issue') };
      default:
        return { color: 'default', label: type };
    }
  };

  const config = getChipConfig(type);
  return <Chip label={config.label} color={config.color} size="small" />;
};

const StatusChip = ({ status, t }) => {
  const getStatusConfig = (status) => {
    switch (status) {
      case 'pending':
        return { color: 'warning', label: t('status.pending', 'Pending') };
      case 'resolved':
        return { color: 'success', label: t('status.resolved', 'Resolved') };
      default:
        return { color: 'default', label: status || 'Unknown' };
    }
  };

  const config = getStatusConfig(status);
  return <Chip label={config.label} color={config.color} size="small" variant="outlined" />;
};

// Load resource bundles once
if (!i18n.hasResourceBundle('en', 'adminMeetingIssues')) {
  i18n.addResourceBundle('en', 'adminMeetingIssues', enBundle, true, true);
  i18n.addResourceBundle('ar', 'adminMeetingIssues', arBundle, true, true);
}

const MeetingIssues = () => {
  const { t } = useTranslation('adminMeetingIssues');
  const [loading, setLoading] = useState(true);
  const [issues, setIssues] = useState([]);
  const [selectedIssue, setSelectedIssue] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [replyText, setReplyText] = useState('');
  const textareaRef = useRef(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  useEffect(() => {
    const fetchIssues = async () => {
      try {
        const { data } = await axios.get('/meeting-issues');
        if (data.success) setIssues(data.data);
      } catch (err) {
        console.error('Failed to fetch issues', err);
        setSnackbar({ open: true, message: t('errors.fetch_failed', 'فشل في جلب البيانات'), severity: 'error' });
      } finally {
        setLoading(false);
      }
    };
    fetchIssues();
  }, [t]);

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (dateTime) => {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const formatTime = (dateTime) => {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleViewDetails = useCallback((issue) => {
    console.log('Selected issue:', issue);
    console.log('Admin reply:', issue.admin_reply);
    setSelectedIssue(issue);
    setReplyText(issue.admin_reply || '');
    setDetailsOpen(true);
    // Focus the textarea after it's rendered
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }, 100);
  }, []);

  const handleCloseDetails = useCallback(() => {
    setDetailsOpen(false);
    // Don't clear selectedIssue here to avoid UI flicker during close animation
    setTimeout(() => {
      setSelectedIssue(null);
      setReplyText('');
    }, 300);
  }, []);

  const handleResolveIssue = useCallback(async (issueId) => {
    const currentText = textareaRef.current?.value || '';
    if (!currentText.trim()) {
      setSnackbar({
        open: true,
        message: t('errors.reply_required', 'الرجاء إدخال رد'),
        severity: 'warning'
      });
      return;
    }

    setUpdatingStatus(true);
    try {
      const { data } = await axios.put(`/meeting-issues/${issueId}/resolve`, { reply: currentText });
      if (data.success) {
        setIssues(prevIssues =>
          prevIssues.map(issue =>
            issue.id === issueId
              ? { ...issue, status: 'resolved', updated_at: new Date().toISOString(), admin_reply: currentText }
              : issue
          )
        );
        
        if (selectedIssue?.id === issueId) {
          setSelectedIssue(prev => ({
            ...prev,
            status: 'resolved',
            updated_at: new Date().toISOString(),
            admin_reply: currentText
          }));
        }
        
        setSnackbar({ 
          open: true, 
          message: t('success.issue_resolved', 'تم حل المشكلة بنجاح'), 
          severity: 'success' 
        });
        
        // Close the dialog after successful resolution
        setTimeout(() => {
          setDetailsOpen(false);
          setReplyText('');
        }, 1000);
      }
    } catch (err) {
      console.error('Failed to resolve issue', err);
      setSnackbar({ 
        open: true, 
        message: t('errors.resolve_failed', 'فشل في حل المشكلة'), 
        severity: 'error' 
      });
    } finally {
      setUpdatingStatus(false);
    }
  }, [selectedIssue, t]);

  const handleCloseSnackbar = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  const DetailsDialog = useMemo(() => () => (
    <Dialog 
      open={detailsOpen} 
      onClose={handleCloseDetails}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <InfoIcon color="primary" />
          {t('details.title', 'تفاصيل المشكلة')} #{selectedIssue?.id}
        </Box>
      </DialogTitle>
      <DialogContent>
        {selectedIssue && (
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                {t('details.basic_info', 'المعلومات الأساسية')}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.issue_type', 'نوع المشكلة')}
                  </Typography>
                  <IssueChip type={selectedIssue.issue_type} t={t} />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.status', 'الحالة')}
                  </Typography>
                  <StatusChip status={selectedIssue.status} t={t} />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.issue_source', 'المصدر')}
                  </Typography>
                  <Chip 
                    label={selectedIssue.issue_source === 'booking' ? 'حجز' : selectedIssue.issue_source === 'meeting' ? 'اجتماع' : 'غير محدد'} 
                    size="small" 
                    variant="outlined"
                    color={selectedIssue.issue_source === 'booking' ? 'primary' : 'secondary'}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.description', 'الوصف')}
                  </Typography>
                  <Typography variant="body2">
                    {selectedIssue.description || t('details.no_description', 'لا يوجد وصف')}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <Divider />
            </Grid>

            {/* User Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                {t('details.user_info', 'معلومات المستخدم')}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.user', 'المستخدم')}
                  </Typography>
                  <Typography variant="body2">
                    {selectedIssue.user_name || '-'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.user_email', 'البريد الإلكتروني')}
                  </Typography>
                  <Typography variant="body2">
                    {selectedIssue.user_email || '-'}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <Divider />
            </Grid>

            {/* Meeting Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                {t('details.meeting_info', 'معلومات الاجتماع')}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.meeting_name', 'اسم الاجتماع')}
                  </Typography>
                  <Typography variant="body2">
                    {selectedIssue.meeting_name || '-'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.room_name', 'اسم الغرفة')}
                  </Typography>
                  <Typography variant="body2">
                    {selectedIssue.room_name || '-'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.meeting_date', 'تاريخ الاجتماع')}
                  </Typography>
                  <Typography variant="body2">
                    {formatDate(selectedIssue.meeting_date)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.meeting_time', 'وقت الاجتماع')}
                  </Typography>
                  <Typography variant="body2">
                    {formatTime(selectedIssue.meeting_date)}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <Divider />
            </Grid>

            {/* Booking Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                {t('details.booking_info', 'معلومات الحجز')}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.booking_datetime', 'تاريخ الحجز')}
                  </Typography>
                  <Typography variant="body2">
                    {formatDateTime(selectedIssue.booking_datetime)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.booking_status', 'حالة الحجز')}
                  </Typography>
                  <Chip 
                    label={selectedIssue.booking_status || '-'} 
                    size="small" 
                    variant="outlined"
                    color={
                      selectedIssue.booking_status === 'completed' ? 'success' :
                      selectedIssue.booking_status === 'issue_reported' ? 'error' :
                      selectedIssue.booking_status === 'ongoing' ? 'warning' :
                      'default'
                    }
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <Divider />
            </Grid>

            {/* Admin Reply */}
            <Grid item xs={12}>
              {selectedIssue.status === 'pending' ? (
                <TextField
                  key={`reply-${selectedIssue.id}`}
                  inputRef={textareaRef}
                  label={t('details.admin_reply', 'رد الأدمن')}
                  defaultValue={replyText}
                  fullWidth
                  multiline
                  minRows={3}
                  variant="outlined"
                  margin="normal"
                  onBlur={(e) => setReplyText(e.target.value)}
                />
              ) : (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    {t('details.previous_reply', 'الرد المرسل سابقاً')}
                  </Typography>
                  {selectedIssue.admin_reply ? (
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 2,
                        backgroundColor: '#f5f5f5',
                        border: '1px solid #e0e0e0',
                        borderRadius: 1
                      }}
                    >
                      <Typography variant="body1" style={{ whiteSpace: 'pre-wrap' }}>
                        {selectedIssue.admin_reply}
                      </Typography>
                    </Paper>
                  ) : (
                    <Typography variant="body2" color="textSecondary" fontStyle="italic">
                      {t('details.no_reply', 'لم يتم إرسال رد')}
                    </Typography>
                  )}
                </Box>
              )}
            </Grid>

            {/* Timestamps */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                {t('details.timestamps', 'التواريخ')}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.created_at', 'تاريخ الإنشاء')}
                  </Typography>
                  <Typography variant="body2">
                    {formatDateTime(selectedIssue.created_at)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('columns.updated_at', 'تاريخ التحديث')}
                  </Typography>
                  <Typography variant="body2">
                    {formatDateTime(selectedIssue.updated_at)}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        )}
      </DialogContent>
      <DialogActions>
        {selectedIssue?.status === 'pending' && (
          <Button 
            onClick={() => handleResolveIssue(selectedIssue.id)}
            disabled={updatingStatus}
            color="success"
            startIcon={updatingStatus ? <CircularProgress size={16} /> : <CheckCircleIcon />}
          >
            {updatingStatus ? t('actions.resolving', 'جاري الحل...') : t('actions.resolve', 'حل المشكلة')}
          </Button>
        )}
        <Button onClick={handleCloseDetails} color="primary">
          {t('details.close', 'إغلاق')}
        </Button>
      </DialogActions>
    </Dialog>
  ), [selectedIssue, detailsOpen, replyText, t, handleCloseDetails, handleResolveIssue, updatingStatus]);

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" gutterBottom>
          {t('title', 'إدارة مشاكل الاجتماعات')}
        </Typography>
        {loading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Paper>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('columns.id', 'ID')}</TableCell>
                  <TableCell>{t('columns.issue_type', 'نوع المشكلة')}</TableCell>
                  <TableCell>{t('columns.status', 'الحالة')}</TableCell>
                  <TableCell>{t('columns.user', 'المستخدم')}</TableCell>
                  <TableCell>{t('columns.meeting_name', 'اسم الاجتماع')}</TableCell>
                  <TableCell>{t('columns.created_at', 'تاريخ الإنشاء')}</TableCell>
                  <TableCell>{t('columns.actions', 'الإجراءات')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {issues.map(issue => (
                  <TableRow key={issue.id} hover>
                    <TableCell>{issue.id}</TableCell>
                    <TableCell>
                      <IssueChip type={issue.issue_type} t={t} />
                    </TableCell>
                    <TableCell>
                      <StatusChip status={issue.status} t={t} />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        <PersonIcon fontSize="small" />
                        {issue.user_name || '-'}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        <VideoCallIcon fontSize="small" />
                        {issue.meeting_name || '-'}
                      </Box>
                    </TableCell>
                    <TableCell>{formatDateTime(issue.created_at)}</TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Tooltip title={t('details.view', 'عرض التفاصيل')}>
                          <IconButton 
                            size="small" 
                            onClick={() => handleViewDetails(issue)}
                            color="primary"
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                        {issue.status === 'pending' && (
                          <Tooltip title={t('actions.resolve', 'حل المشكلة')}>
                            <IconButton 
                              size="small" 
                              onClick={() => handleResolveIssue(issue.id)}
                              disabled={updatingStatus}
                              color="success"
                            >
                              {updatingStatus ? <CircularProgress size={16} /> : <CheckCircleIcon />}
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Paper>
        )}
        <DetailsDialog />
        <Snackbar 
          open={snackbar.open} 
          autoHideDuration={6000} 
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </Layout>
  );
};

export default MeetingIssues;
