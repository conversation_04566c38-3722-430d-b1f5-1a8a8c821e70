{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{Container,Typography,Box,Paper,CircularProgress,Alert,Dialog,DialogTitle,DialogContent,DialogActions,Button,Avatar,Chip,Grid,useTheme,IconButton,Tooltip}from'@mui/material';import{CalendarToday as CalendarIcon,AccessTime as TimeIcon,Person as PersonIcon,AttachMoney as MoneyIcon,ChevronLeft as ChevronLeftIcon,ChevronRight as ChevronRightIcon,VideoCall as VideoCallIcon,Cancel as CancelIcon,Close as CloseIcon}from'@mui/icons-material';import axios from'axios';import{format,startOfWeek,addDays,addWeeks,subWeeks}from'date-fns';import{ar,enUS}from'date-fns/locale';import{useAuth}from'../../contexts/AuthContext';import Layout from'../../components/Layout';import WeeklyBookingsTable from'../../components/WeeklyBookingsTable';import{convertFromDatabaseTime,formatDateInStudentTimezone,getCurrentTimeInTimezone}from'../../utils/timezone';import moment from'moment-timezone';import VideoSDKMeeting from'../../components/meeting/VideoSDKMeeting';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TeacherBookings=()=>{const{t,i18n}=useTranslation();const{token}=useAuth();const theme=useTheme();const isRtl=i18n.language==='ar';const[bookings,setBookings]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState('');const[selectedBooking,setSelectedBooking]=useState(null);const[detailsDialogOpen,setDetailsDialogOpen]=useState(false);const[teacherProfile,setTeacherProfile]=useState(null);const[cancelDialogOpen,setCancelDialogOpen]=useState(false);const[cancellingBooking,setCancellingBooking]=useState(false);const[openMeeting,setOpenMeeting]=useState(false);const[currentMeeting,setCurrentMeeting]=useState(null);const[currentTime,setCurrentTime]=useState(new Date());// Week navigation\nconst[currentWeekStart,setCurrentWeekStart]=useState(()=>{const today=new Date();return startOfWeek(today,{weekStartsOn:1});// Start from current week\n});// Days of the week (format expected by WeeklyBookingsTable)\nconst daysOfWeek=['monday','tuesday','wednesday','thursday','friday','saturday','sunday'];// Week navigation functions\nconst goToPreviousWeek=()=>{const previousWeek=subWeeks(currentWeekStart,1);setCurrentWeekStart(previousWeek);};const goToNextWeek=()=>{const nextWeek=addWeeks(currentWeekStart,1);const today=new Date();const oneYearAhead=addWeeks(today,52);// One year ahead from today\nconst maxWeek=startOfWeek(oneYearAhead,{weekStartsOn:1});// Don't allow going beyond one year ahead\nif(nextWeek<=maxWeek){setCurrentWeekStart(nextWeek);}};// Check if navigation buttons should be disabled\nconst isPreviousWeekDisabled=()=>false;const isNextWeekDisabled=()=>{const nextWeek=addWeeks(currentWeekStart,1);const today=new Date();const oneYearAhead=addWeeks(today,52);// One year ahead from today\nconst maxWeek=startOfWeek(oneYearAhead,{weekStartsOn:1});return nextWeek>maxWeek;};// Update current time every second\nuseEffect(()=>{const timeInterval=setInterval(()=>{setCurrentTime(new Date());},1000);return()=>clearInterval(timeInterval);},[]);// Fetch bookings\nuseEffect(()=>{const fetchBookings=async()=>{try{setLoading(true);const{data}=await axios.get('/api/bookings/teacher',{headers:{'Authorization':`Bearer ${token}`}});if(data.success){console.log('Teacher bookings data:',data.data);// Make sure all bookings have the correct data types\nconst processedBookings=data.data.map(booking=>({...booking,price_per_lesson:parseFloat(booking.price_per_lesson||0),duration:booking.duration?String(booking.duration):'50'}));console.log('Processed teacher bookings:',processedBookings);setBookings(processedBookings);// Set teacher profile with timezone information\nif(data.teacherTimezone){setTeacherProfile({timezone:data.teacherTimezone});}}else{setError(data.message||t('bookings.fetchError'));}}catch(error){var _error$response,_error$response$data;console.error('Error fetching teacher bookings:',error);setError(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||t('bookings.fetchError'));}finally{setLoading(false);}};if(token){fetchBookings();}},[token,t]);// Handle view details\nconst handleViewDetails=booking=>{setSelectedBooking(booking);setDetailsDialogOpen(true);};// Handle cancel booking\nconst handleCancelBookingClick=booking=>{setSelectedBooking(booking);setCancelDialogOpen(true);};// Handle join meeting\nconst handleJoinMeeting=async booking=>{try{// Check if room_name exists from the booking data\nif(!booking.room_name){console.error('No room_name found for booking:',booking);alert(t('meetings.noRoomError')||'Meeting room not found');return;}// Check if meeting_id exists\nif(!booking.meeting_id){console.error('No meeting_id found for booking:',booking);alert(t('meetings.noMeetingError')||'Meeting ID not found');return;}console.log('Joining meeting with data:',{room_name:booking.room_name,meeting_id:booking.meeting_id,datetime:booking.datetime,duration:booking.duration});// Validate room\nconst response=await axios.get(`/meetings/${booking.room_name}/validate`);setCurrentMeeting({...booking,room_name:booking.room_name});setOpenMeeting(true);}catch(error){console.error('Error joining meeting:',error);alert(t('meetings.joinError'));}};const handleCloseMeeting=()=>{setOpenMeeting(false);setCurrentMeeting(null);};// Get meeting status from database directly\nconst getMeetingStatus=booking=>{return booking.status||'scheduled';};// Check if user can join meeting\nconst canJoinMeeting=booking=>{if(!booking||!teacherProfile)return false;const currentStatus=getMeetingStatus(booking);if(currentStatus==='cancelled'||currentStatus==='completed'){return false;}const meetingStartTime=new Date(booking.datetime);const meetingEndTime=new Date(booking.datetime);meetingEndTime.setMinutes(meetingEndTime.getMinutes()+parseInt(booking.duration));const now=new Date();return now>=meetingStartTime&&now<meetingEndTime;};// Handle booking cancellation\nconst handleCancelBooking=async()=>{if(!selectedBooking)return;try{setCancellingBooking(true);const{data}=await axios.put(`/bookings/${selectedBooking.id}/cancel`,{},{headers:{'Authorization':`Bearer ${token}`}});if(data.success){// Update the booking status in the local state\nsetBookings(prevBookings=>prevBookings.map(booking=>booking.id===selectedBooking.id?{...booking,status:'cancelled'}:booking));alert(t('bookings.cancelSuccess'));}else{alert(data.message||t('bookings.cancelError'));}}catch(error){var _error$response2,_error$response2$data;console.error('Error cancelling booking:',error);alert(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||t('bookings.cancelError'));}finally{setCancellingBooking(false);setCancelDialogOpen(false);setDetailsDialogOpen(false);setSelectedBooking(null);}};// Get status chip color\nconst getStatusColor=status=>{switch(status){case'scheduled':return'primary';case'completed':return'success';case'cancelled':return'error';case'issue_reported':return'warning';case'ongoing':return'info';default:return'default';}};// Get translated status text\nconst getStatusText=status=>{return t(`bookings.statusValues.${status}`,{defaultValue:status.charAt(0).toUpperCase()+status.slice(1)});};// Format booking date in teacher's timezone\nconst formatBookingDate=datetime=>{if(!teacherProfile||!teacherProfile.timezone){return format(new Date(datetime),'PPP',{locale:isRtl?ar:enUS});}const formattedDate=formatDateInStudentTimezone(datetime,teacherProfile.timezone,'YYYY-MM-DD');return moment(formattedDate,'YYYY-MM-DD').format('MMMM D, YYYY');};// Format booking time in teacher's timezone\nconst formatBookingTime=datetime=>{if(!teacherProfile||!teacherProfile.timezone){return format(new Date(datetime),'p',{locale:isRtl?ar:enUS});}const formattedDateTime=formatDateInStudentTimezone(datetime,teacherProfile.timezone,'YYYY-MM-DD HH:mm:ss');return moment(formattedDateTime,'YYYY-MM-DD HH:mm:ss').format('h:mm A');};// Calculate lesson price based on duration\nconst calculateLessonPrice=(pricePerLesson,duration)=>{const durationNum=parseInt(duration,10);return durationNum===50?pricePerLesson:pricePerLesson/2;};// Render details dialog\nconst renderDetailsDialog=()=>{if(!selectedBooking)return null;const lessonPrice=calculateLessonPrice(selectedBooking.price_per_lesson,selectedBooking.duration);return/*#__PURE__*/_jsxs(Dialog,{open:detailsDialogOpen,onClose:()=>setDetailsDialogOpen(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{bgcolor:'primary.main',color:'white'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(CalendarIcon,{}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:t('bookings.bookingDetails')})]})}),/*#__PURE__*/_jsx(DialogContent,{sx:{mt:2},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Avatar,{src:selectedBooking.student_picture,alt:selectedBooking.student_name,sx:{mr:2,width:56,height:56}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:selectedBooking.student_name}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:selectedBooking.student_email})]})]})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(CalendarIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.date')})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:formatBookingDate(selectedBooking.datetime)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(TimeIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.time')})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:formatBookingTime(selectedBooking.datetime)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(TimeIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.duration')})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",children:[selectedBooking.duration,\" \",t('bookings.minutes')]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(MoneyIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.price')})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",children:[\"$\",lessonPrice.toFixed(2)]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(PersonIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:t('bookings.status')})]}),/*#__PURE__*/_jsx(Chip,{label:getStatusText(selectedBooking.status),color:getStatusColor(selectedBooking.status),variant:\"filled\"})]})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailsDialogOpen(false),children:t('common.close')}),selectedBooking&&/*#__PURE__*/_jsx(Button,{onClick:()=>canJoinMeeting(selectedBooking)&&handleJoinMeeting(selectedBooking),color:canJoinMeeting(selectedBooking)?\"success\":\"inherit\",variant:canJoinMeeting(selectedBooking)?\"contained\":\"outlined\",startIcon:/*#__PURE__*/_jsx(VideoCallIcon,{}),disabled:!canJoinMeeting(selectedBooking),sx:{mr:1,...(canJoinMeeting(selectedBooking)?{}:{color:theme.palette.grey[500],borderColor:theme.palette.grey[300],backgroundColor:theme.palette.grey[100],'&:hover':{backgroundColor:theme.palette.grey[200]}})},children:canJoinMeeting(selectedBooking)?t('meetings.join'):t('meetings.notStarted')}),(selectedBooking===null||selectedBooking===void 0?void 0:selectedBooking.status)==='scheduled'&&/*#__PURE__*/_jsx(Button,{onClick:()=>{setDetailsDialogOpen(false);setCancelDialogOpen(true);},color:\"error\",variant:\"contained\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),children:t('bookings.cancel')})]})]});};// Cancel confirmation dialog\nconst renderCancelDialog=()=>/*#__PURE__*/_jsxs(Dialog,{open:cancelDialogOpen,onClose:()=>setCancelDialogOpen(false),children:[/*#__PURE__*/_jsxs(DialogTitle,{children:[t('bookings.confirmCancel'),/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:()=>setCancelDialogOpen(false),sx:{position:'absolute',right:8,top:8},children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:t('bookings.cancelWarning')}),selectedBooking&&/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.student'),\":\"]}),\" \",selectedBooking.student_name]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.date'),\":\"]}),\" \",formatBookingDate(selectedBooking.datetime)]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.time'),\":\"]}),\" \",formatBookingTime(selectedBooking.datetime)]})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setCancelDialogOpen(false),children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{onClick:handleCancelBooking,color:\"error\",variant:\"contained\",disabled:cancellingBooking,children:cancellingBooking?t('bookings.cancelling'):t('bookings.confirmCancelButton')})]})]});return/*#__PURE__*/_jsxs(Layout,{children:[/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",sx:{py:4},children:[/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{p:3,mb:4,bgcolor:'primary.main',color:'white'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',flexWrap:'wrap',gap:2},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,sx:{fontWeight:'bold'},children:t('teacher.weeklyBookings')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{opacity:0.9},children:t('teacher.weeklyBookingsDescription')})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'right'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{opacity:0.8,mb:0.5},children:t('booking.weekNavigation')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:'bold'},children:[\"\\uD83D\\uDCC5 \",format(currentWeekStart,'MMM d',{locale:isRtl?ar:enUS}),\" - \",format(addDays(currentWeekStart,6),'MMM d, yyyy',{locale:isRtl?ar:enUS})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1},children:[/*#__PURE__*/_jsx(Tooltip,{title:t('booking.previousWeek'),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:goToPreviousWeek,disabled:isPreviousWeekDisabled(),sx:{color:'white',bgcolor:'rgba(255, 255, 255, 0.1)','&:hover':{bgcolor:'rgba(255, 255, 255, 0.2)'},'&:disabled':{color:'rgba(255, 255, 255, 0.3)',bgcolor:'rgba(255, 255, 255, 0.05)'}},children:/*#__PURE__*/_jsx(ChevronLeftIcon,{})})})}),/*#__PURE__*/_jsx(Tooltip,{title:t('booking.nextWeek'),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:goToNextWeek,disabled:isNextWeekDisabled(),sx:{color:'white',bgcolor:'rgba(255, 255, 255, 0.1)','&:hover':{bgcolor:'rgba(255, 255, 255, 0.2)'},'&:disabled':{color:'rgba(255, 255, 255, 0.3)',bgcolor:'rgba(255, 255, 255, 0.05)'}},children:/*#__PURE__*/_jsx(ChevronRightIcon,{})})})})]})]})]})}),/*#__PURE__*/_jsx(Paper,{elevation:2,sx:{p:2,mb:3,bgcolor:'background.paper'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Box,{sx:{bgcolor:'primary.main',color:'white',p:1,borderRadius:1,minWidth:40,textAlign:'center'},children:\"\\uD83D\\uDD50\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:0.5},children:t('bookings.currentTime')}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:'bold'},children:teacherProfile!==null&&teacherProfile!==void 0&&teacherProfile.timezone?moment(formatDateInStudentTimezone(new Date().toISOString(),teacherProfile.timezone,'YYYY-MM-DD HH:mm:ss'),'YYYY-MM-DD HH:mm:ss').format('dddd, MMMM D, YYYY [at] h:mm A'):format(new Date(),'PPpp',{locale:isRtl?ar:enUS})}),(teacherProfile===null||teacherProfile===void 0?void 0:teacherProfile.timezone)&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{opacity:0.8},children:teacherProfile.timezone})]})]})}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',py:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}):error?/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:4},children:error}):/*#__PURE__*/_jsx(WeeklyBookingsTable,{bookings:bookings,loading:loading,currentWeekStart:currentWeekStart,daysOfWeek:daysOfWeek,onViewDetails:handleViewDetails,onCancelBooking:handleCancelBookingClick// Now teachers can cancel bookings\n,studentProfile:teacherProfile// Pass teacher profile for timezone conversion\n,formatBookingTime:formatBookingTime,getStatusColor:getStatusColor,getStatusText:getStatusText,isTeacherView:true// Add this prop to distinguish teacher view\n}),renderDetailsDialog(),renderCancelDialog()]}),/*#__PURE__*/_jsx(Dialog,{fullScreen:true,open:openMeeting,onClose:handleCloseMeeting,children:currentMeeting&&/*#__PURE__*/_jsx(VideoSDKMeeting,{roomId:currentMeeting.room_name,meetingId:currentMeeting.meeting_id,meetingData:currentMeeting,onClose:handleCloseMeeting})})]});};export default TeacherBookings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Container", "Typography", "Box", "Paper", "CircularProgress", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Avatar", "Chip", "Grid", "useTheme", "IconButton", "<PERSON><PERSON><PERSON>", "CalendarToday", "CalendarIcon", "AccessTime", "TimeIcon", "Person", "PersonIcon", "AttachMoney", "MoneyIcon", "ChevronLeft", "ChevronLeftIcon", "ChevronRight", "ChevronRightIcon", "VideoCall", "VideoCallIcon", "Cancel", "CancelIcon", "Close", "CloseIcon", "axios", "format", "startOfWeek", "addDays", "addWeeks", "subWeeks", "ar", "enUS", "useAuth", "Layout", "WeeklyBookingsTable", "convertFromDatabaseTime", "formatDateInStudentTimezone", "getCurrentTimeInTimezone", "moment", "VideoSDKMeeting", "jsx", "_jsx", "jsxs", "_jsxs", "TeacherBookings", "t", "i18n", "token", "theme", "isRtl", "language", "bookings", "setBookings", "loading", "setLoading", "error", "setError", "selectedBooking", "setSelectedBooking", "detailsDialogOpen", "setDetailsDialogOpen", "teacher<PERSON><PERSON><PERSON><PERSON>", "setTeacherProfile", "cancelDialogOpen", "setCancelDialogOpen", "cancellingBooking", "setCancellingBooking", "openMeeting", "setOpenMeeting", "currentMeeting", "setCurrentMeeting", "currentTime", "setCurrentTime", "Date", "currentWeekStart", "setCurrentWeekStart", "today", "weekStartsOn", "daysOfWeek", "goToPreviousWeek", "previousWeek", "goToNextWeek", "nextWeek", "oneYearAhead", "maxWeek", "isPreviousWeekDisabled", "isNextWeekDisabled", "timeInterval", "setInterval", "clearInterval", "fetchBookings", "data", "get", "headers", "success", "console", "log", "processedBookings", "map", "booking", "price_per_lesson", "parseFloat", "duration", "String", "teacherTimezone", "timezone", "message", "_error$response", "_error$response$data", "response", "handleViewDetails", "handleCancelBookingClick", "handleJoinMeeting", "room_name", "alert", "meeting_id", "datetime", "handleCloseMeeting", "getMeetingStatus", "status", "canJoinMeeting", "currentStatus", "meetingStartTime", "meetingEndTime", "setMinutes", "getMinutes", "parseInt", "now", "handleCancelBooking", "put", "id", "prevBookings", "_error$response2", "_error$response2$data", "getStatusColor", "getStatusText", "defaultValue", "char<PERSON>t", "toUpperCase", "slice", "formatBookingDate", "locale", "formattedDate", "formatBookingTime", "formattedDateTime", "calculateLessonPrice", "pricePer<PERSON><PERSON>on", "durationNum", "renderDetailsDialog", "lessonPrice", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "sx", "bgcolor", "color", "display", "alignItems", "gap", "variant", "mt", "container", "spacing", "item", "xs", "mb", "src", "student_picture", "alt", "student_name", "mr", "width", "height", "student_email", "sm", "toFixed", "label", "onClick", "startIcon", "disabled", "palette", "grey", "borderColor", "backgroundColor", "renderCancelDialog", "position", "right", "top", "gutterBottom", "py", "elevation", "p", "justifyContent", "flexWrap", "fontWeight", "opacity", "textAlign", "title", "borderRadius", "min<PERSON><PERSON><PERSON>", "toISOString", "severity", "onViewDetails", "onCancelBooking", "studentProfile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullScreen", "roomId", "meetingId", "meetingData"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/teacher/Bookings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Container,\n  Typography,\n  Box,\n  Paper,\n  CircularProgress,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Avatar,\n  Chip,\n  Grid,\n  useTheme,\n  IconButton,\n  Tooltip\n} from '@mui/material';\nimport {\n  CalendarToday as CalendarIcon,\n  AccessTime as TimeIcon,\n  Person as PersonIcon,\n  AttachMoney as MoneyIcon,\n  ChevronLeft as ChevronLeftIcon,\n  ChevronRight as ChevronRightIcon,\n  VideoCall as VideoCallIcon,\n  Cancel as CancelIcon,\n  Close as CloseIcon\n} from '@mui/icons-material';\nimport axios from 'axios';\nimport { format, startOfWeek, addDays, addWeeks, subWeeks } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Layout from '../../components/Layout';\nimport WeeklyBookingsTable from '../../components/WeeklyBookingsTable';\nimport { convertFromDatabaseTime, formatDateInStudentTimezone, getCurrentTimeInTimezone } from '../../utils/timezone';\nimport moment from 'moment-timezone';\nimport VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';\n\nconst TeacherBookings = () => {\n  const { t, i18n } = useTranslation();\n  const { token } = useAuth();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n\n  const [bookings, setBookings] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedBooking, setSelectedBooking] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n  const [teacherProfile, setTeacherProfile] = useState(null);\n  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);\n  const [cancellingBooking, setCancellingBooking] = useState(false);\n  const [openMeeting, setOpenMeeting] = useState(false);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  // Week navigation\n  const [currentWeekStart, setCurrentWeekStart] = useState(() => {\n    const today = new Date();\n    return startOfWeek(today, { weekStartsOn: 1 }); // Start from current week\n  });\n\n  // Days of the week (format expected by WeeklyBookingsTable)\n  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n\n  // Week navigation functions\n  const goToPreviousWeek = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    setCurrentWeekStart(previousWeek);\n  };\n\n  const goToNextWeek = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n\n    // Don't allow going beyond one year ahead\n    if (nextWeek <= maxWeek) {\n      setCurrentWeekStart(nextWeek);\n    }\n  };\n\n  // Check if navigation buttons should be disabled\n  const isPreviousWeekDisabled = () => false;\n\n  const isNextWeekDisabled = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n    return nextWeek > maxWeek;\n  };\n\n  // Update current time every second\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Fetch bookings\n  useEffect(() => {\n    const fetchBookings = async () => {\n      try {\n        setLoading(true);\n        const { data } = await axios.get('/api/bookings/teacher', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success) {\n          console.log('Teacher bookings data:', data.data);\n\n          // Make sure all bookings have the correct data types\n          const processedBookings = data.data.map(booking => ({\n            ...booking,\n            price_per_lesson: parseFloat(booking.price_per_lesson || 0),\n            duration: booking.duration ? String(booking.duration) : '50'\n          }));\n\n          console.log('Processed teacher bookings:', processedBookings);\n          setBookings(processedBookings);\n\n          // Set teacher profile with timezone information\n          if (data.teacherTimezone) {\n            setTeacherProfile({ timezone: data.teacherTimezone });\n          }\n        } else {\n          setError(data.message || t('bookings.fetchError'));\n        }\n      } catch (error) {\n        console.error('Error fetching teacher bookings:', error);\n        setError(error.response?.data?.message || t('bookings.fetchError'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (token) {\n      fetchBookings();\n    }\n  }, [token, t]);\n\n  // Handle view details\n  const handleViewDetails = (booking) => {\n    setSelectedBooking(booking);\n    setDetailsDialogOpen(true);\n  };\n\n  // Handle cancel booking\n  const handleCancelBookingClick = (booking) => {\n    setSelectedBooking(booking);\n    setCancelDialogOpen(true);\n  };\n\n  // Handle join meeting\n  const handleJoinMeeting = async (booking) => {\n    try {\n      // Check if room_name exists from the booking data\n      if (!booking.room_name) {\n        console.error('No room_name found for booking:', booking);\n        alert(t('meetings.noRoomError') || 'Meeting room not found');\n        return;\n      }\n\n      // Check if meeting_id exists\n      if (!booking.meeting_id) {\n        console.error('No meeting_id found for booking:', booking);\n        alert(t('meetings.noMeetingError') || 'Meeting ID not found');\n        return;\n      }\n\n      console.log('Joining meeting with data:', {\n        room_name: booking.room_name,\n        meeting_id: booking.meeting_id,\n        datetime: booking.datetime,\n        duration: booking.duration\n      });\n\n      // Validate room\n      const response = await axios.get(`/meetings/${booking.room_name}/validate`);\n      setCurrentMeeting({ ...booking, room_name: booking.room_name });\n      setOpenMeeting(true);\n    } catch (error) {\n      console.error('Error joining meeting:', error);\n      alert(t('meetings.joinError'));\n    }\n  };\n\n  const handleCloseMeeting = () => {\n    setOpenMeeting(false);\n    setCurrentMeeting(null);\n  };\n\n  // Get meeting status from database directly\n  const getMeetingStatus = (booking) => {\n    return booking.status || 'scheduled';\n  };\n\n  // Check if user can join meeting\n  const canJoinMeeting = (booking) => {\n    if (!booking || !teacherProfile) return false;\n\n    const currentStatus = getMeetingStatus(booking);\n    if (currentStatus === 'cancelled' || currentStatus === 'completed') {\n      return false;\n    }\n\n    const meetingStartTime = new Date(booking.datetime);\n    const meetingEndTime = new Date(booking.datetime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));\n    const now = new Date();\n\n    return now >= meetingStartTime && now < meetingEndTime;\n  };\n\n  // Handle booking cancellation\n  const handleCancelBooking = async () => {\n    if (!selectedBooking) return;\n\n    try {\n      setCancellingBooking(true);\n      const { data } = await axios.put(`/bookings/${selectedBooking.id}/cancel`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        // Update the booking status in the local state\n        setBookings(prevBookings =>\n          prevBookings.map(booking =>\n            booking.id === selectedBooking.id\n              ? { ...booking, status: 'cancelled' }\n              : booking\n          )\n        );\n        alert(t('bookings.cancelSuccess'));\n      } else {\n        alert(data.message || t('bookings.cancelError'));\n      }\n    } catch (error) {\n      console.error('Error cancelling booking:', error);\n      alert(error.response?.data?.message || t('bookings.cancelError'));\n    } finally {\n      setCancellingBooking(false);\n      setCancelDialogOpen(false);\n      setDetailsDialogOpen(false);\n      setSelectedBooking(null);\n    }\n  };\n\n  // Get status chip color\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'scheduled':\n        return 'primary';\n      case 'completed':\n        return 'success';\n      case 'cancelled':\n        return 'error';\n      case 'issue_reported':\n        return 'warning';\n      case 'ongoing':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n\n  // Get translated status text\n  const getStatusText = (status) => {\n    return t(`bookings.statusValues.${status}`, { \n      defaultValue: status.charAt(0).toUpperCase() + status.slice(1) \n    });\n  };\n\n  // Format booking date in teacher's timezone\n  const formatBookingDate = (datetime) => {\n    if (!teacherProfile || !teacherProfile.timezone) {\n      return format(new Date(datetime), 'PPP', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDate = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD');\n    return moment(formattedDate, 'YYYY-MM-DD').format('MMMM D, YYYY');\n  };\n\n  // Format booking time in teacher's timezone\n  const formatBookingTime = (datetime) => {\n    if (!teacherProfile || !teacherProfile.timezone) {\n      return format(new Date(datetime), 'p', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDateTime = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    return moment(formattedDateTime, 'YYYY-MM-DD HH:mm:ss').format('h:mm A');\n  };\n\n  // Calculate lesson price based on duration\n  const calculateLessonPrice = (pricePerLesson, duration) => {\n    const durationNum = parseInt(duration, 10);\n    return durationNum === 50 ? pricePerLesson : pricePerLesson / 2;\n  };\n\n  // Render details dialog\n  const renderDetailsDialog = () => {\n    if (!selectedBooking) return null;\n\n    const lessonPrice = calculateLessonPrice(selectedBooking.price_per_lesson, selectedBooking.duration);\n\n    return (\n      <Dialog\n        open={detailsDialogOpen}\n        onClose={() => setDetailsDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <CalendarIcon />\n            <Typography variant=\"h6\">\n              {t('bookings.bookingDetails')}\n            </Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent sx={{ mt: 2 }}>\n          <Grid container spacing={3}>\n            <Grid item xs={12}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <Avatar\n                  src={selectedBooking.student_picture}\n                  alt={selectedBooking.student_name}\n                  sx={{ mr: 2, width: 56, height: 56 }}\n                />\n                <Box>\n                  <Typography variant=\"h6\">\n                    {selectedBooking.student_name}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {selectedBooking.student_email}\n                  </Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <CalendarIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.date')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                {formatBookingDate(selectedBooking.datetime)}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.time')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                {formatBookingTime(selectedBooking.datetime)}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.duration')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                {selectedBooking.duration} {t('bookings.minutes')}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <MoneyIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.price')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                ${lessonPrice.toFixed(2)}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.status')}\n                </Typography>\n              </Box>\n              <Chip\n                label={getStatusText(selectedBooking.status)}\n                color={getStatusColor(selectedBooking.status)}\n                variant=\"filled\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDetailsDialogOpen(false)}>\n            {t('common.close')}\n          </Button>\n\n          {/* Join Meeting Button */}\n          {selectedBooking && (\n            <Button\n              onClick={() => canJoinMeeting(selectedBooking) && handleJoinMeeting(selectedBooking)}\n              color={canJoinMeeting(selectedBooking) ? \"success\" : \"inherit\"}\n              variant={canJoinMeeting(selectedBooking) ? \"contained\" : \"outlined\"}\n              startIcon={<VideoCallIcon />}\n              disabled={!canJoinMeeting(selectedBooking)}\n              sx={{\n                mr: 1,\n                ...(canJoinMeeting(selectedBooking) ? {} : {\n                  color: theme.palette.grey[500],\n                  borderColor: theme.palette.grey[300],\n                  backgroundColor: theme.palette.grey[100],\n                  '&:hover': {\n                    backgroundColor: theme.palette.grey[200],\n                  }\n                })\n              }}\n            >\n              {canJoinMeeting(selectedBooking) ? t('meetings.join') : t('meetings.notStarted')}\n            </Button>\n          )}\n\n          {selectedBooking?.status === 'scheduled' && (\n            <Button\n              onClick={() => {\n                setDetailsDialogOpen(false);\n                setCancelDialogOpen(true);\n              }}\n              color=\"error\"\n              variant=\"contained\"\n              startIcon={<CancelIcon />}\n            >\n              {t('bookings.cancel')}\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Cancel confirmation dialog\n  const renderCancelDialog = () => (\n    <Dialog open={cancelDialogOpen} onClose={() => setCancelDialogOpen(false)}>\n      <DialogTitle>\n        {t('bookings.confirmCancel')}\n        <IconButton\n          aria-label=\"close\"\n          onClick={() => setCancelDialogOpen(false)}\n          sx={{ position: 'absolute', right: 8, top: 8 }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n      <DialogContent>\n        <Typography variant=\"body1\">\n          {t('bookings.cancelWarning')}\n        </Typography>\n        {selectedBooking && (\n          <Box sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.student')}:</strong> {selectedBooking.student_name}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.date')}:</strong> {formatBookingDate(selectedBooking.datetime)}\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>{t('bookings.time')}:</strong> {formatBookingTime(selectedBooking.datetime)}\n            </Typography>\n          </Box>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={() => setCancelDialogOpen(false)}>\n          {t('common.cancel')}\n        </Button>\n        <Button\n          onClick={handleCancelBooking}\n          color=\"error\"\n          variant=\"contained\"\n          disabled={cancellingBooking}\n        >\n          {cancellingBooking ? t('bookings.cancelling') : t('bookings.confirmCancelButton')}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Layout>\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        <Paper elevation={3} sx={{ p: 3, mb: 4, bgcolor: 'primary.main', color: 'white' }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>\n            <Box>\n              <Typography variant=\"h4\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                {t('teacher.weeklyBookings')}\n              </Typography>\n              <Typography variant=\"body1\" sx={{ opacity: 0.9 }}>\n                {t('teacher.weeklyBookingsDescription')}\n              </Typography>\n            </Box>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <Box sx={{ textAlign: 'right' }}>\n                <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 0.5 }}>\n                  {t('booking.weekNavigation')}\n                </Typography>\n                <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                  📅 {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                </Typography>\n              </Box>\n              <Box sx={{ display: 'flex', gap: 1 }}>\n                <Tooltip title={t('booking.previousWeek')}>\n                  <span>\n                    <IconButton\n                      onClick={goToPreviousWeek}\n                      disabled={isPreviousWeekDisabled()}\n                      sx={{\n                        color: 'white',\n                        bgcolor: 'rgba(255, 255, 255, 0.1)',\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 255, 255, 0.2)',\n                        },\n                        '&:disabled': {\n                          color: 'rgba(255, 255, 255, 0.3)',\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        }\n                      }}\n                    >\n                      <ChevronLeftIcon />\n                    </IconButton>\n                  </span>\n                </Tooltip>\n                <Tooltip title={t('booking.nextWeek')}>\n                  <span>\n                    <IconButton\n                      onClick={goToNextWeek}\n                      disabled={isNextWeekDisabled()}\n                      sx={{\n                        color: 'white',\n                        bgcolor: 'rgba(255, 255, 255, 0.1)',\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 255, 255, 0.2)',\n                        },\n                        '&:disabled': {\n                          color: 'rgba(255, 255, 255, 0.3)',\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        }\n                      }}\n                    >\n                      <ChevronRightIcon />\n                    </IconButton>\n                  </span>\n                </Tooltip>\n              </Box>\n            </Box>\n          </Box>\n        </Paper>\n\n        {/* Current Time Display */}\n        <Paper elevation={2} sx={{ p: 2, mb: 3, bgcolor: 'background.paper' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <Box sx={{\n              bgcolor: 'primary.main',\n              color: 'white',\n              p: 1,\n              borderRadius: 1,\n              minWidth: 40,\n              textAlign: 'center'\n            }}>\n              🕐\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 0.5 }}>\n                {t('bookings.currentTime')}\n              </Typography>\n              <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                {teacherProfile?.timezone ? (\n                  moment(formatDateInStudentTimezone(new Date().toISOString(), teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('dddd, MMMM D, YYYY [at] h:mm A')\n                ) : (\n                  format(new Date(), 'PPpp', {\n                    locale: isRtl ? ar : enUS\n                  })\n                )}\n              </Typography>\n              {teacherProfile?.timezone && (\n                <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                  {teacherProfile.timezone}\n                </Typography>\n              )}\n            </Box>\n          </Box>\n        </Paper>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>\n            <CircularProgress />\n          </Box>\n        ) : error ? (\n          <Alert severity=\"error\" sx={{ mb: 4 }}>\n            {error}\n          </Alert>\n        ) : (\n          <WeeklyBookingsTable\n            bookings={bookings}\n            loading={loading}\n            currentWeekStart={currentWeekStart}\n            daysOfWeek={daysOfWeek}\n            onViewDetails={handleViewDetails}\n            onCancelBooking={handleCancelBookingClick} // Now teachers can cancel bookings\n            studentProfile={teacherProfile} // Pass teacher profile for timezone conversion\n            formatBookingTime={formatBookingTime}\n            getStatusColor={getStatusColor}\n            getStatusText={getStatusText}\n            isTeacherView={true} // Add this prop to distinguish teacher view\n          />\n        )}\n\n        {renderDetailsDialog()}\n        {renderCancelDialog()}\n      </Container>\n\n      {/* Meeting Dialog */}\n      <Dialog\n        fullScreen\n        open={openMeeting}\n        onClose={handleCloseMeeting}\n      >\n        {currentMeeting && (\n          <VideoSDKMeeting\n            roomId={currentMeeting.room_name}\n            meetingId={currentMeeting.meeting_id}\n            meetingData={currentMeeting}\n            onClose={handleCloseMeeting}\n          />\n        )}\n      </Dialog>\n    </Layout>\n  );\n};\n\nexport default TeacherBookings;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,gBAAgB,CAChBC,KAAK,CACLC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,MAAM,CACNC,MAAM,CACNC,IAAI,CACJC,IAAI,CACJC,QAAQ,CACRC,UAAU,CACVC,OAAO,KACF,eAAe,CACtB,OACEC,aAAa,GAAI,CAAAC,YAAY,CAC7BC,UAAU,GAAI,CAAAC,QAAQ,CACtBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,WAAW,GAAI,CAAAC,SAAS,CACxBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,YAAY,GAAI,CAAAC,gBAAgB,CAChCC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,KAAK,GAAI,CAAAC,SAAS,KACb,qBAAqB,CAC5B,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,WAAW,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,QAAQ,KAAQ,UAAU,CAC3E,OAASC,EAAE,CAAEC,IAAI,KAAQ,iBAAiB,CAC1C,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,mBAAmB,KAAM,sCAAsC,CACtE,OAASC,uBAAuB,CAAEC,2BAA2B,CAAEC,wBAAwB,KAAQ,sBAAsB,CACrH,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CACpC,MAAO,CAAAC,eAAe,KAAM,0CAA0C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvE,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG1D,cAAc,CAAC,CAAC,CACpC,KAAM,CAAE2D,KAAM,CAAC,CAAGf,OAAO,CAAC,CAAC,CAC3B,KAAM,CAAAgB,KAAK,CAAG7C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA8C,KAAK,CAAGH,IAAI,CAACI,QAAQ,GAAK,IAAI,CAEpC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACmE,OAAO,CAAEC,UAAU,CAAC,CAAGpE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqE,KAAK,CAAEC,QAAQ,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACuE,eAAe,CAAEC,kBAAkB,CAAC,CAAGxE,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACyE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC2E,cAAc,CAAEC,iBAAiB,CAAC,CAAG5E,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC6E,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG9E,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC+E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhF,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACiF,WAAW,CAAEC,cAAc,CAAC,CAAGlF,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACmF,cAAc,CAAEC,iBAAiB,CAAC,CAAGpF,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACqF,WAAW,CAAEC,cAAc,CAAC,CAAGtF,QAAQ,CAAC,GAAI,CAAAuF,IAAI,CAAC,CAAC,CAAC,CAE1D;AACA,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzF,QAAQ,CAAC,IAAM,CAC7D,KAAM,CAAA0F,KAAK,CAAG,GAAI,CAAAH,IAAI,CAAC,CAAC,CACxB,MAAO,CAAA/C,WAAW,CAACkD,KAAK,CAAE,CAAEC,YAAY,CAAE,CAAE,CAAC,CAAC,CAAE;AAClD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAG,CAAC,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CAEjG;AACA,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,YAAY,CAAGnD,QAAQ,CAAC6C,gBAAgB,CAAE,CAAC,CAAC,CAClDC,mBAAmB,CAACK,YAAY,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,QAAQ,CAAGtD,QAAQ,CAAC8C,gBAAgB,CAAE,CAAC,CAAC,CAC9C,KAAM,CAAAE,KAAK,CAAG,GAAI,CAAAH,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAU,YAAY,CAAGvD,QAAQ,CAACgD,KAAK,CAAE,EAAE,CAAC,CAAE;AAC1C,KAAM,CAAAQ,OAAO,CAAG1D,WAAW,CAACyD,YAAY,CAAE,CAAEN,YAAY,CAAE,CAAE,CAAC,CAAC,CAE9D;AACA,GAAIK,QAAQ,EAAIE,OAAO,CAAE,CACvBT,mBAAmB,CAACO,QAAQ,CAAC,CAC/B,CACF,CAAC,CAED;AACA,KAAM,CAAAG,sBAAsB,CAAGA,CAAA,GAAM,KAAK,CAE1C,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAJ,QAAQ,CAAGtD,QAAQ,CAAC8C,gBAAgB,CAAE,CAAC,CAAC,CAC9C,KAAM,CAAAE,KAAK,CAAG,GAAI,CAAAH,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAU,YAAY,CAAGvD,QAAQ,CAACgD,KAAK,CAAE,EAAE,CAAC,CAAE;AAC1C,KAAM,CAAAQ,OAAO,CAAG1D,WAAW,CAACyD,YAAY,CAAE,CAAEN,YAAY,CAAE,CAAE,CAAC,CAAC,CAC9D,MAAO,CAAAK,QAAQ,CAAGE,OAAO,CAC3B,CAAC,CAED;AACAjG,SAAS,CAAC,IAAM,CACd,KAAM,CAAAoG,YAAY,CAAGC,WAAW,CAAC,IAAM,CACrChB,cAAc,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,CAC5B,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMgB,aAAa,CAACF,YAAY,CAAC,CAC1C,CAAC,CAAE,EAAE,CAAC,CAEN;AACApG,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuG,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFpC,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEqC,IAAK,CAAC,CAAG,KAAM,CAAAnE,KAAK,CAACoE,GAAG,CAAC,uBAAuB,CAAE,CACxDC,OAAO,CAAE,CACP,eAAe,CAAE,UAAU9C,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI4C,IAAI,CAACG,OAAO,CAAE,CAChBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEL,IAAI,CAACA,IAAI,CAAC,CAEhD;AACA,KAAM,CAAAM,iBAAiB,CAAGN,IAAI,CAACA,IAAI,CAACO,GAAG,CAACC,OAAO,GAAK,CAClD,GAAGA,OAAO,CACVC,gBAAgB,CAAEC,UAAU,CAACF,OAAO,CAACC,gBAAgB,EAAI,CAAC,CAAC,CAC3DE,QAAQ,CAAEH,OAAO,CAACG,QAAQ,CAAGC,MAAM,CAACJ,OAAO,CAACG,QAAQ,CAAC,CAAG,IAC1D,CAAC,CAAC,CAAC,CAEHP,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEC,iBAAiB,CAAC,CAC7D7C,WAAW,CAAC6C,iBAAiB,CAAC,CAE9B;AACA,GAAIN,IAAI,CAACa,eAAe,CAAE,CACxB1C,iBAAiB,CAAC,CAAE2C,QAAQ,CAAEd,IAAI,CAACa,eAAgB,CAAC,CAAC,CACvD,CACF,CAAC,IAAM,CACLhD,QAAQ,CAACmC,IAAI,CAACe,OAAO,EAAI7D,CAAC,CAAC,qBAAqB,CAAC,CAAC,CACpD,CACF,CAAE,MAAOU,KAAK,CAAE,KAAAoD,eAAA,CAAAC,oBAAA,CACdb,OAAO,CAACxC,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxDC,QAAQ,CAAC,EAAAmD,eAAA,CAAApD,KAAK,CAACsD,QAAQ,UAAAF,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBhB,IAAI,UAAAiB,oBAAA,iBAApBA,oBAAA,CAAsBF,OAAO,GAAI7D,CAAC,CAAC,qBAAqB,CAAC,CAAC,CACrE,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIP,KAAK,CAAE,CACT2C,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAAE,CAAC3C,KAAK,CAAEF,CAAC,CAAC,CAAC,CAEd;AACA,KAAM,CAAAiE,iBAAiB,CAAIX,OAAO,EAAK,CACrCzC,kBAAkB,CAACyC,OAAO,CAAC,CAC3BvC,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAmD,wBAAwB,CAAIZ,OAAO,EAAK,CAC5CzC,kBAAkB,CAACyC,OAAO,CAAC,CAC3BnC,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAgD,iBAAiB,CAAG,KAAO,CAAAb,OAAO,EAAK,CAC3C,GAAI,CACF;AACA,GAAI,CAACA,OAAO,CAACc,SAAS,CAAE,CACtBlB,OAAO,CAACxC,KAAK,CAAC,iCAAiC,CAAE4C,OAAO,CAAC,CACzDe,KAAK,CAACrE,CAAC,CAAC,sBAAsB,CAAC,EAAI,wBAAwB,CAAC,CAC5D,OACF,CAEA;AACA,GAAI,CAACsD,OAAO,CAACgB,UAAU,CAAE,CACvBpB,OAAO,CAACxC,KAAK,CAAC,kCAAkC,CAAE4C,OAAO,CAAC,CAC1De,KAAK,CAACrE,CAAC,CAAC,yBAAyB,CAAC,EAAI,sBAAsB,CAAC,CAC7D,OACF,CAEAkD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAE,CACxCiB,SAAS,CAAEd,OAAO,CAACc,SAAS,CAC5BE,UAAU,CAAEhB,OAAO,CAACgB,UAAU,CAC9BC,QAAQ,CAAEjB,OAAO,CAACiB,QAAQ,CAC1Bd,QAAQ,CAAEH,OAAO,CAACG,QACpB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAArF,KAAK,CAACoE,GAAG,CAAC,aAAaO,OAAO,CAACc,SAAS,WAAW,CAAC,CAC3E3C,iBAAiB,CAAC,CAAE,GAAG6B,OAAO,CAAEc,SAAS,CAAEd,OAAO,CAACc,SAAU,CAAC,CAAC,CAC/D7C,cAAc,CAAC,IAAI,CAAC,CACtB,CAAE,MAAOb,KAAK,CAAE,CACdwC,OAAO,CAACxC,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C2D,KAAK,CAACrE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAwE,kBAAkB,CAAGA,CAAA,GAAM,CAC/BjD,cAAc,CAAC,KAAK,CAAC,CACrBE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAAgD,gBAAgB,CAAInB,OAAO,EAAK,CACpC,MAAO,CAAAA,OAAO,CAACoB,MAAM,EAAI,WAAW,CACtC,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAIrB,OAAO,EAAK,CAClC,GAAI,CAACA,OAAO,EAAI,CAACtC,cAAc,CAAE,MAAO,MAAK,CAE7C,KAAM,CAAA4D,aAAa,CAAGH,gBAAgB,CAACnB,OAAO,CAAC,CAC/C,GAAIsB,aAAa,GAAK,WAAW,EAAIA,aAAa,GAAK,WAAW,CAAE,CAClE,MAAO,MAAK,CACd,CAEA,KAAM,CAAAC,gBAAgB,CAAG,GAAI,CAAAjD,IAAI,CAAC0B,OAAO,CAACiB,QAAQ,CAAC,CACnD,KAAM,CAAAO,cAAc,CAAG,GAAI,CAAAlD,IAAI,CAAC0B,OAAO,CAACiB,QAAQ,CAAC,CACjDO,cAAc,CAACC,UAAU,CAACD,cAAc,CAACE,UAAU,CAAC,CAAC,CAAGC,QAAQ,CAAC3B,OAAO,CAACG,QAAQ,CAAC,CAAC,CACnF,KAAM,CAAAyB,GAAG,CAAG,GAAI,CAAAtD,IAAI,CAAC,CAAC,CAEtB,MAAO,CAAAsD,GAAG,EAAIL,gBAAgB,EAAIK,GAAG,CAAGJ,cAAc,CACxD,CAAC,CAED;AACA,KAAM,CAAAK,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAACvE,eAAe,CAAE,OAEtB,GAAI,CACFS,oBAAoB,CAAC,IAAI,CAAC,CAC1B,KAAM,CAAEyB,IAAK,CAAC,CAAG,KAAM,CAAAnE,KAAK,CAACyG,GAAG,CAAC,aAAaxE,eAAe,CAACyE,EAAE,SAAS,CAAE,CAAC,CAAC,CAAE,CAC7ErC,OAAO,CAAE,CACP,eAAe,CAAE,UAAU9C,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI4C,IAAI,CAACG,OAAO,CAAE,CAChB;AACA1C,WAAW,CAAC+E,YAAY,EACtBA,YAAY,CAACjC,GAAG,CAACC,OAAO,EACtBA,OAAO,CAAC+B,EAAE,GAAKzE,eAAe,CAACyE,EAAE,CAC7B,CAAE,GAAG/B,OAAO,CAAEoB,MAAM,CAAE,WAAY,CAAC,CACnCpB,OACN,CACF,CAAC,CACDe,KAAK,CAACrE,CAAC,CAAC,wBAAwB,CAAC,CAAC,CACpC,CAAC,IAAM,CACLqE,KAAK,CAACvB,IAAI,CAACe,OAAO,EAAI7D,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAClD,CACF,CAAE,MAAOU,KAAK,CAAE,KAAA6E,gBAAA,CAAAC,qBAAA,CACdtC,OAAO,CAACxC,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD2D,KAAK,CAAC,EAAAkB,gBAAA,CAAA7E,KAAK,CAACsD,QAAQ,UAAAuB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBzC,IAAI,UAAA0C,qBAAA,iBAApBA,qBAAA,CAAsB3B,OAAO,GAAI7D,CAAC,CAAC,sBAAsB,CAAC,CAAC,CACnE,CAAC,OAAS,CACRqB,oBAAoB,CAAC,KAAK,CAAC,CAC3BF,mBAAmB,CAAC,KAAK,CAAC,CAC1BJ,oBAAoB,CAAC,KAAK,CAAC,CAC3BF,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAA4E,cAAc,CAAIf,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,WAAW,CACd,MAAO,OAAO,CAChB,IAAK,gBAAgB,CACnB,MAAO,SAAS,CAClB,IAAK,SAAS,CACZ,MAAO,MAAM,CACf,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED;AACA,KAAM,CAAAgB,aAAa,CAAIhB,MAAM,EAAK,CAChC,MAAO,CAAA1E,CAAC,CAAC,yBAAyB0E,MAAM,EAAE,CAAE,CAC1CiB,YAAY,CAAEjB,MAAM,CAACkB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGnB,MAAM,CAACoB,KAAK,CAAC,CAAC,CAC/D,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAIxB,QAAQ,EAAK,CACtC,GAAI,CAACvD,cAAc,EAAI,CAACA,cAAc,CAAC4C,QAAQ,CAAE,CAC/C,MAAO,CAAAhF,MAAM,CAAC,GAAI,CAAAgD,IAAI,CAAC2C,QAAQ,CAAC,CAAE,KAAK,CAAE,CAAEyB,MAAM,CAAE5F,KAAK,CAAGnB,EAAE,CAAGC,IAAK,CAAC,CAAC,CACzE,CAEA,KAAM,CAAA+G,aAAa,CAAG1G,2BAA2B,CAACgF,QAAQ,CAAEvD,cAAc,CAAC4C,QAAQ,CAAE,YAAY,CAAC,CAClG,MAAO,CAAAnE,MAAM,CAACwG,aAAa,CAAE,YAAY,CAAC,CAACrH,MAAM,CAAC,cAAc,CAAC,CACnE,CAAC,CAED;AACA,KAAM,CAAAsH,iBAAiB,CAAI3B,QAAQ,EAAK,CACtC,GAAI,CAACvD,cAAc,EAAI,CAACA,cAAc,CAAC4C,QAAQ,CAAE,CAC/C,MAAO,CAAAhF,MAAM,CAAC,GAAI,CAAAgD,IAAI,CAAC2C,QAAQ,CAAC,CAAE,GAAG,CAAE,CAAEyB,MAAM,CAAE5F,KAAK,CAAGnB,EAAE,CAAGC,IAAK,CAAC,CAAC,CACvE,CAEA,KAAM,CAAAiH,iBAAiB,CAAG5G,2BAA2B,CAACgF,QAAQ,CAAEvD,cAAc,CAAC4C,QAAQ,CAAE,qBAAqB,CAAC,CAC/G,MAAO,CAAAnE,MAAM,CAAC0G,iBAAiB,CAAE,qBAAqB,CAAC,CAACvH,MAAM,CAAC,QAAQ,CAAC,CAC1E,CAAC,CAED;AACA,KAAM,CAAAwH,oBAAoB,CAAGA,CAACC,cAAc,CAAE5C,QAAQ,GAAK,CACzD,KAAM,CAAA6C,WAAW,CAAGrB,QAAQ,CAACxB,QAAQ,CAAE,EAAE,CAAC,CAC1C,MAAO,CAAA6C,WAAW,GAAK,EAAE,CAAGD,cAAc,CAAGA,cAAc,CAAG,CAAC,CACjE,CAAC,CAED;AACA,KAAM,CAAAE,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAI,CAAC3F,eAAe,CAAE,MAAO,KAAI,CAEjC,KAAM,CAAA4F,WAAW,CAAGJ,oBAAoB,CAACxF,eAAe,CAAC2C,gBAAgB,CAAE3C,eAAe,CAAC6C,QAAQ,CAAC,CAEpG,mBACE3D,KAAA,CAAChD,MAAM,EACL2J,IAAI,CAAE3F,iBAAkB,CACxB4F,OAAO,CAAEA,CAAA,GAAM3F,oBAAoB,CAAC,KAAK,CAAE,CAC3C4F,QAAQ,CAAC,IAAI,CACbC,SAAS,MAAAC,QAAA,eAETjH,IAAA,CAAC7C,WAAW,EAAC+J,EAAE,CAAE,CAAEC,OAAO,CAAE,cAAc,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAH,QAAA,cAC3D/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eACzDjH,IAAA,CAAClC,YAAY,GAAE,CAAC,cAChBkC,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAAAP,QAAA,CACrB7G,CAAC,CAAC,yBAAyB,CAAC,CACnB,CAAC,EACV,CAAC,CACK,CAAC,cACdJ,IAAA,CAAC5C,aAAa,EAAC8J,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,cAC3B/G,KAAA,CAACzC,IAAI,EAACiK,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAV,QAAA,eACzBjH,IAAA,CAACvC,IAAI,EAACmK,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAZ,QAAA,cAChB/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxDjH,IAAA,CAACzC,MAAM,EACLwK,GAAG,CAAE/G,eAAe,CAACgH,eAAgB,CACrCC,GAAG,CAAEjH,eAAe,CAACkH,YAAa,CAClChB,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,MAAM,CAAE,EAAG,CAAE,CACtC,CAAC,cACFnI,KAAA,CAACpD,GAAG,EAAAmK,QAAA,eACFjH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAAAP,QAAA,CACrBjG,eAAe,CAACkH,YAAY,CACnB,CAAC,cACblI,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CAC/CjG,eAAe,CAACsH,aAAa,CACpB,CAAC,EACV,CAAC,EACH,CAAC,CACF,CAAC,cAEPpI,KAAA,CAACzC,IAAI,EAACmK,IAAI,MAACC,EAAE,CAAE,EAAG,CAACU,EAAE,CAAE,CAAE,CAAAtB,QAAA,eACvB/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxDjH,IAAA,CAAClC,YAAY,EAACoJ,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cACtDpH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnD7G,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,EACV,CAAC,cACNJ,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAAP,QAAA,CACxBd,iBAAiB,CAACnF,eAAe,CAAC2D,QAAQ,CAAC,CAClC,CAAC,EACT,CAAC,cAEPzE,KAAA,CAACzC,IAAI,EAACmK,IAAI,MAACC,EAAE,CAAE,EAAG,CAACU,EAAE,CAAE,CAAE,CAAAtB,QAAA,eACvB/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxDjH,IAAA,CAAChC,QAAQ,EAACkJ,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cAClDpH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnD7G,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,EACV,CAAC,cACNJ,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAAP,QAAA,CACxBX,iBAAiB,CAACtF,eAAe,CAAC2D,QAAQ,CAAC,CAClC,CAAC,EACT,CAAC,cAEPzE,KAAA,CAACzC,IAAI,EAACmK,IAAI,MAACC,EAAE,CAAE,EAAG,CAACU,EAAE,CAAE,CAAE,CAAAtB,QAAA,eACvB/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxDjH,IAAA,CAAChC,QAAQ,EAACkJ,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cAClDpH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnD7G,CAAC,CAAC,mBAAmB,CAAC,CACb,CAAC,EACV,CAAC,cACNF,KAAA,CAACrD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAAP,QAAA,EACxBjG,eAAe,CAAC6C,QAAQ,CAAC,GAAC,CAACzD,CAAC,CAAC,kBAAkB,CAAC,EACvC,CAAC,EACT,CAAC,cAEPF,KAAA,CAACzC,IAAI,EAACmK,IAAI,MAACC,EAAE,CAAE,EAAG,CAACU,EAAE,CAAE,CAAE,CAAAtB,QAAA,eACvB/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxDjH,IAAA,CAAC5B,SAAS,EAAC8I,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cACnDpH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnD7G,CAAC,CAAC,gBAAgB,CAAC,CACV,CAAC,EACV,CAAC,cACNF,KAAA,CAACrD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAAP,QAAA,EAAC,GACzB,CAACL,WAAW,CAAC4B,OAAO,CAAC,CAAC,CAAC,EACd,CAAC,EACT,CAAC,cAEPtI,KAAA,CAACzC,IAAI,EAACmK,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAZ,QAAA,eAChB/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxDjH,IAAA,CAAC9B,UAAU,EAACgJ,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEf,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cACpDpH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAACJ,KAAK,CAAC,gBAAgB,CAAAH,QAAA,CACnD7G,CAAC,CAAC,iBAAiB,CAAC,CACX,CAAC,EACV,CAAC,cACNJ,IAAA,CAACxC,IAAI,EACHiL,KAAK,CAAE3C,aAAa,CAAC9E,eAAe,CAAC8D,MAAM,CAAE,CAC7CsC,KAAK,CAAEvB,cAAc,CAAC7E,eAAe,CAAC8D,MAAM,CAAE,CAC9C0C,OAAO,CAAC,QAAQ,CACjB,CAAC,EACE,CAAC,EACH,CAAC,CACM,CAAC,cAChBtH,KAAA,CAAC7C,aAAa,EAAA4J,QAAA,eACZjH,IAAA,CAAC1C,MAAM,EAACoL,OAAO,CAAEA,CAAA,GAAMvH,oBAAoB,CAAC,KAAK,CAAE,CAAA8F,QAAA,CAChD7G,CAAC,CAAC,cAAc,CAAC,CACZ,CAAC,CAGRY,eAAe,eACdhB,IAAA,CAAC1C,MAAM,EACLoL,OAAO,CAAEA,CAAA,GAAM3D,cAAc,CAAC/D,eAAe,CAAC,EAAIuD,iBAAiB,CAACvD,eAAe,CAAE,CACrFoG,KAAK,CAAErC,cAAc,CAAC/D,eAAe,CAAC,CAAG,SAAS,CAAG,SAAU,CAC/DwG,OAAO,CAAEzC,cAAc,CAAC/D,eAAe,CAAC,CAAG,WAAW,CAAG,UAAW,CACpE2H,SAAS,cAAE3I,IAAA,CAACtB,aAAa,GAAE,CAAE,CAC7BkK,QAAQ,CAAE,CAAC7D,cAAc,CAAC/D,eAAe,CAAE,CAC3CkG,EAAE,CAAE,CACFiB,EAAE,CAAE,CAAC,CACL,IAAIpD,cAAc,CAAC/D,eAAe,CAAC,CAAG,CAAC,CAAC,CAAG,CACzCoG,KAAK,CAAE7G,KAAK,CAACsI,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC,CAC9BC,WAAW,CAAExI,KAAK,CAACsI,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC,CACpCE,eAAe,CAAEzI,KAAK,CAACsI,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC,CACxC,SAAS,CAAE,CACTE,eAAe,CAAEzI,KAAK,CAACsI,OAAO,CAACC,IAAI,CAAC,GAAG,CACzC,CACF,CAAC,CACH,CAAE,CAAA7B,QAAA,CAEDlC,cAAc,CAAC/D,eAAe,CAAC,CAAGZ,CAAC,CAAC,eAAe,CAAC,CAAGA,CAAC,CAAC,qBAAqB,CAAC,CAC1E,CACT,CAEA,CAAAY,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE8D,MAAM,IAAK,WAAW,eACtC9E,IAAA,CAAC1C,MAAM,EACLoL,OAAO,CAAEA,CAAA,GAAM,CACbvH,oBAAoB,CAAC,KAAK,CAAC,CAC3BI,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CACF6F,KAAK,CAAC,OAAO,CACbI,OAAO,CAAC,WAAW,CACnBmB,SAAS,cAAE3I,IAAA,CAACpB,UAAU,GAAE,CAAE,CAAAqI,QAAA,CAEzB7G,CAAC,CAAC,iBAAiB,CAAC,CACf,CACT,EACY,CAAC,EACV,CAAC,CAEb,CAAC,CAED;AACA,KAAM,CAAA6I,kBAAkB,CAAGA,CAAA,gBACzB/I,KAAA,CAAChD,MAAM,EAAC2J,IAAI,CAAEvF,gBAAiB,CAACwF,OAAO,CAAEA,CAAA,GAAMvF,mBAAmB,CAAC,KAAK,CAAE,CAAA0F,QAAA,eACxE/G,KAAA,CAAC/C,WAAW,EAAA8J,QAAA,EACT7G,CAAC,CAAC,wBAAwB,CAAC,cAC5BJ,IAAA,CAACrC,UAAU,EACT,aAAW,OAAO,CAClB+K,OAAO,CAAEA,CAAA,GAAMnH,mBAAmB,CAAC,KAAK,CAAE,CAC1C2F,EAAE,CAAE,CAAEgC,QAAQ,CAAE,UAAU,CAAEC,KAAK,CAAE,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAnC,QAAA,cAE/CjH,IAAA,CAAClB,SAAS,GAAE,CAAC,CACH,CAAC,EACF,CAAC,cACdoB,KAAA,CAAC9C,aAAa,EAAA6J,QAAA,eACZjH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAAP,QAAA,CACxB7G,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,CACZY,eAAe,eACdd,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,eACjB/G,KAAA,CAACrD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAC6B,YAAY,MAAApC,QAAA,eACtC/G,KAAA,WAAA+G,QAAA,EAAS7G,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACY,eAAe,CAACkH,YAAY,EAC7D,CAAC,cACbhI,KAAA,CAACrD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAC6B,YAAY,MAAApC,QAAA,eACtC/G,KAAA,WAAA+G,QAAA,EAAS7G,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC+F,iBAAiB,CAACnF,eAAe,CAAC2D,QAAQ,CAAC,EACzE,CAAC,cACbzE,KAAA,CAACrD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAAP,QAAA,eACzB/G,KAAA,WAAA+G,QAAA,EAAS7G,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACkG,iBAAiB,CAACtF,eAAe,CAAC2D,QAAQ,CAAC,EACzE,CAAC,EACV,CACN,EACY,CAAC,cAChBzE,KAAA,CAAC7C,aAAa,EAAA4J,QAAA,eACZjH,IAAA,CAAC1C,MAAM,EAACoL,OAAO,CAAEA,CAAA,GAAMnH,mBAAmB,CAAC,KAAK,CAAE,CAAA0F,QAAA,CAC/C7G,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,cACTJ,IAAA,CAAC1C,MAAM,EACLoL,OAAO,CAAEnD,mBAAoB,CAC7B6B,KAAK,CAAC,OAAO,CACbI,OAAO,CAAC,WAAW,CACnBoB,QAAQ,CAAEpH,iBAAkB,CAAAyF,QAAA,CAE3BzF,iBAAiB,CAAGpB,CAAC,CAAC,qBAAqB,CAAC,CAAGA,CAAC,CAAC,8BAA8B,CAAC,CAC3E,CAAC,EACI,CAAC,EACV,CACT,CAED,mBACEF,KAAA,CAACV,MAAM,EAAAyH,QAAA,eACL/G,KAAA,CAACtD,SAAS,EAACmK,QAAQ,CAAC,IAAI,CAACG,EAAE,CAAE,CAAEoC,EAAE,CAAE,CAAE,CAAE,CAAArC,QAAA,eACrCjH,IAAA,CAACjD,KAAK,EAACwM,SAAS,CAAE,CAAE,CAACrC,EAAE,CAAE,CAAEsC,CAAC,CAAE,CAAC,CAAE1B,EAAE,CAAE,CAAC,CAAEX,OAAO,CAAE,cAAc,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAH,QAAA,cAChF/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEoC,cAAc,CAAE,eAAe,CAAEnC,UAAU,CAAE,QAAQ,CAAEoC,QAAQ,CAAE,MAAM,CAAEnC,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eAC5G/G,KAAA,CAACpD,GAAG,EAAAmK,QAAA,eACFjH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAAC6B,YAAY,MAACnC,EAAE,CAAE,CAAEyC,UAAU,CAAE,MAAO,CAAE,CAAA1C,QAAA,CAC9D7G,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,cACbJ,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAACN,EAAE,CAAE,CAAE0C,OAAO,CAAE,GAAI,CAAE,CAAA3C,QAAA,CAC9C7G,CAAC,CAAC,mCAAmC,CAAC,CAC7B,CAAC,EACV,CAAC,cACNF,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eACzD/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAE2C,SAAS,CAAE,OAAQ,CAAE,CAAA5C,QAAA,eAC9BjH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAACN,EAAE,CAAE,CAAE0C,OAAO,CAAE,GAAG,CAAE9B,EAAE,CAAE,GAAI,CAAE,CAAAb,QAAA,CACvD7G,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,cACbF,KAAA,CAACrD,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAACN,EAAE,CAAE,CAAEyC,UAAU,CAAE,MAAO,CAAE,CAAA1C,QAAA,EAAC,eAChD,CAACjI,MAAM,CAACiD,gBAAgB,CAAE,OAAO,CAAE,CAAEmE,MAAM,CAAE5F,KAAK,CAAGnB,EAAE,CAAGC,IAAK,CAAC,CAAC,CAAC,KAAG,CAACN,MAAM,CAACE,OAAO,CAAC+C,gBAAgB,CAAE,CAAC,CAAC,CAAE,aAAa,CAAE,CAAEmE,MAAM,CAAE5F,KAAK,CAAGnB,EAAE,CAAGC,IAAK,CAAC,CAAC,EACjJ,CAAC,EACV,CAAC,cACNY,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEE,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eACnCjH,IAAA,CAACpC,OAAO,EAACkM,KAAK,CAAE1J,CAAC,CAAC,sBAAsB,CAAE,CAAA6G,QAAA,cACxCjH,IAAA,SAAAiH,QAAA,cACEjH,IAAA,CAACrC,UAAU,EACT+K,OAAO,CAAEpG,gBAAiB,CAC1BsG,QAAQ,CAAEhG,sBAAsB,CAAC,CAAE,CACnCsE,EAAE,CAAE,CACFE,KAAK,CAAE,OAAO,CACdD,OAAO,CAAE,0BAA0B,CACnC,SAAS,CAAE,CACTA,OAAO,CAAE,0BACX,CAAC,CACD,YAAY,CAAE,CACZC,KAAK,CAAE,0BAA0B,CACjCD,OAAO,CAAE,2BACX,CACF,CAAE,CAAAF,QAAA,cAEFjH,IAAA,CAAC1B,eAAe,GAAE,CAAC,CACT,CAAC,CACT,CAAC,CACA,CAAC,cACV0B,IAAA,CAACpC,OAAO,EAACkM,KAAK,CAAE1J,CAAC,CAAC,kBAAkB,CAAE,CAAA6G,QAAA,cACpCjH,IAAA,SAAAiH,QAAA,cACEjH,IAAA,CAACrC,UAAU,EACT+K,OAAO,CAAElG,YAAa,CACtBoG,QAAQ,CAAE/F,kBAAkB,CAAC,CAAE,CAC/BqE,EAAE,CAAE,CACFE,KAAK,CAAE,OAAO,CACdD,OAAO,CAAE,0BAA0B,CACnC,SAAS,CAAE,CACTA,OAAO,CAAE,0BACX,CAAC,CACD,YAAY,CAAE,CACZC,KAAK,CAAE,0BAA0B,CACjCD,OAAO,CAAE,2BACX,CACF,CAAE,CAAAF,QAAA,cAEFjH,IAAA,CAACxB,gBAAgB,GAAE,CAAC,CACV,CAAC,CACT,CAAC,CACA,CAAC,EACP,CAAC,EACH,CAAC,EACH,CAAC,CACD,CAAC,cAGRwB,IAAA,CAACjD,KAAK,EAACwM,SAAS,CAAE,CAAE,CAACrC,EAAE,CAAE,CAAEsC,CAAC,CAAE,CAAC,CAAE1B,EAAE,CAAE,CAAC,CAAEX,OAAO,CAAE,kBAAmB,CAAE,CAAAF,QAAA,cACpE/G,KAAA,CAACpD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAN,QAAA,eACzDjH,IAAA,CAAClD,GAAG,EAACoK,EAAE,CAAE,CACPC,OAAO,CAAE,cAAc,CACvBC,KAAK,CAAE,OAAO,CACdoC,CAAC,CAAE,CAAC,CACJO,YAAY,CAAE,CAAC,CACfC,QAAQ,CAAE,EAAE,CACZH,SAAS,CAAE,QACb,CAAE,CAAA5C,QAAA,CAAC,cAEH,CAAK,CAAC,cACN/G,KAAA,CAACpD,GAAG,EAAAmK,QAAA,eACFjH,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAACF,EAAE,CAAE,CAAEY,EAAE,CAAE,GAAI,CAAE,CAAAb,QAAA,CAChE7G,CAAC,CAAC,sBAAsB,CAAC,CAChB,CAAC,cACbJ,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAACN,EAAE,CAAE,CAAEyC,UAAU,CAAE,MAAO,CAAE,CAAA1C,QAAA,CACjD7F,cAAc,SAAdA,cAAc,WAAdA,cAAc,CAAE4C,QAAQ,CACvBnE,MAAM,CAACF,2BAA2B,CAAC,GAAI,CAAAqC,IAAI,CAAC,CAAC,CAACiI,WAAW,CAAC,CAAC,CAAE7I,cAAc,CAAC4C,QAAQ,CAAE,qBAAqB,CAAC,CAAE,qBAAqB,CAAC,CAAChF,MAAM,CAAC,gCAAgC,CAAC,CAE7KA,MAAM,CAAC,GAAI,CAAAgD,IAAI,CAAC,CAAC,CAAE,MAAM,CAAE,CACzBoE,MAAM,CAAE5F,KAAK,CAAGnB,EAAE,CAAGC,IACvB,CAAC,CACF,CACS,CAAC,CACZ,CAAA8B,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE4C,QAAQ,gBACvBhE,IAAA,CAACnD,UAAU,EAAC2K,OAAO,CAAC,SAAS,CAACN,EAAE,CAAE,CAAE0C,OAAO,CAAE,GAAI,CAAE,CAAA3C,QAAA,CAChD7F,cAAc,CAAC4C,QAAQ,CACd,CACb,EACE,CAAC,EACH,CAAC,CACD,CAAC,CAEPpD,OAAO,cACNZ,IAAA,CAAClD,GAAG,EAACoK,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEoC,cAAc,CAAE,QAAQ,CAAEH,EAAE,CAAE,CAAE,CAAE,CAAArC,QAAA,cAC5DjH,IAAA,CAAChD,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACJ8D,KAAK,cACPd,IAAA,CAAC/C,KAAK,EAACiN,QAAQ,CAAC,OAAO,CAAChD,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CACnCnG,KAAK,CACD,CAAC,cAERd,IAAA,CAACP,mBAAmB,EAClBiB,QAAQ,CAAEA,QAAS,CACnBE,OAAO,CAAEA,OAAQ,CACjBqB,gBAAgB,CAAEA,gBAAiB,CACnCI,UAAU,CAAEA,UAAW,CACvB8H,aAAa,CAAE9F,iBAAkB,CACjC+F,eAAe,CAAE9F,wBAA0B;AAAA,CAC3C+F,cAAc,CAAEjJ,cAAgB;AAAA,CAChCkF,iBAAiB,CAAEA,iBAAkB,CACrCT,cAAc,CAAEA,cAAe,CAC/BC,aAAa,CAAEA,aAAc,CAC7BwE,aAAa,CAAE,IAAM;AAAA,CACtB,CACF,CAEA3D,mBAAmB,CAAC,CAAC,CACrBsC,kBAAkB,CAAC,CAAC,EACZ,CAAC,cAGZjJ,IAAA,CAAC9C,MAAM,EACLqN,UAAU,MACV1D,IAAI,CAAEnF,WAAY,CAClBoF,OAAO,CAAElC,kBAAmB,CAAAqC,QAAA,CAE3BrF,cAAc,eACb5B,IAAA,CAACF,eAAe,EACd0K,MAAM,CAAE5I,cAAc,CAAC4C,SAAU,CACjCiG,SAAS,CAAE7I,cAAc,CAAC8C,UAAW,CACrCgG,WAAW,CAAE9I,cAAe,CAC5BkF,OAAO,CAAElC,kBAAmB,CAC7B,CACF,CACK,CAAC,EACH,CAAC,CAEb,CAAC,CAED,cAAe,CAAAzE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}