{"version": 3, "file": "static/js/reactPlayerMux.339a3458.chunk.js", "mappings": "kFAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAc,CAAC,EAzBJC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAa,CACpBK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,OAC/BC,EAAkBD,EAAQ,MAE9B,MAAMP,UAAYG,EAAaM,UAC7BC,WAAAA,GAAc,IAAAC,EACZC,SAASC,WAAUF,EAAAG,KAEnB1B,EAAc0B,KAAM,WAAW,kBAAaH,EAAKI,MAAMC,WAAQH,UAAQ,IACvEzB,EAAc0B,KAAM,UAAU,kBAAaH,EAAKI,MAAME,UAAOJ,UAAQ,IACrEzB,EAAc0B,KAAM,YAAY,kBAAaH,EAAKI,MAAMG,YAASL,UAAQ,IACzEzB,EAAc0B,KAAM,eAAe,kBAAaH,EAAKI,MAAMI,eAAYN,UAAQ,IAC/EzB,EAAc0B,KAAM,WAAW,kBAAaH,EAAKI,MAAMK,WAAQP,UAAQ,IACvEzB,EAAc0B,KAAM,WAAW,kBAAaH,EAAKI,MAAMM,WAAQR,UAAQ,IACvEzB,EAAc0B,KAAM,WAAW,kBAAaH,EAAKI,MAAMO,WAAQT,UAAQ,IACvEzB,EAAc0B,KAAM,wBAAyBS,GAAUT,KAAKC,MAAMS,qBAAqBD,EAAM3B,OAAO6B,gBACpGrC,EAAc0B,KAAM,eAAe,kBAAaH,EAAKI,MAAMW,eAAYb,UAAQ,IAC/EzB,EAAc0B,KAAM,UAAWa,IAC7Bb,KAAKC,MAAMa,OAAOD,EAAE/B,OAAOiC,YAAY,IAEzCzC,EAAc0B,KAAM,oBAAoB,KACtC,MAAMgB,EAAWhB,KAAKiB,cACtBjB,KAAKC,MAAMiB,WAAWF,EAAS,IAEjC1C,EAAc0B,KAAM,QAAQ,KAC1BA,KAAKmB,OAAOC,OAAQ,CAAI,IAE1B9C,EAAc0B,KAAM,UAAU,KAC5BA,KAAKmB,OAAOC,OAAQ,CAAK,IAE3B9C,EAAc0B,KAAM,OAAQmB,IAC1BnB,KAAKmB,OAASA,CAAM,GAExB,CACAE,iBAAAA,GACErB,KAAKC,MAAMqB,SAAWtB,KAAKC,MAAMqB,QAAQtB,MACzCA,KAAKuB,aAAavB,KAAKmB,QACvB,MAAMK,EAAaxB,KAAKyB,cAAczB,KAAKC,MAAMyB,KAC7CF,IACFxB,KAAKmB,OAAOK,WAAaA,EAE7B,CACAG,oBAAAA,GACE3B,KAAKmB,OAAOK,WAAa,KACzBxB,KAAK4B,gBAAgB5B,KAAKmB,OAC5B,CACAI,YAAAA,CAAaJ,GACX,MAAM,YAAEU,GAAgB7B,KAAKC,MAC7BkB,EAAOW,iBAAiB,OAAQ9B,KAAKG,QACrCgB,EAAOW,iBAAiB,UAAW9B,KAAKI,UACxCe,EAAOW,iBAAiB,UAAW9B,KAAKK,aACxCc,EAAOW,iBAAiB,QAAS9B,KAAKM,SACtCa,EAAOW,iBAAiB,SAAU9B,KAAKc,QACvCK,EAAOW,iBAAiB,QAAS9B,KAAKO,SACtCY,EAAOW,iBAAiB,QAAS9B,KAAKQ,SACtCW,EAAOW,iBAAiB,aAAc9B,KAAK+B,sBAC3CZ,EAAOW,iBAAiB,wBAAyB9B,KAAKY,aACtDO,EAAOW,iBAAiB,wBAAyB9B,KAAKgC,cACtDb,EAAOW,iBAAiB,gCAAiC9B,KAAKiC,0BAC9Dd,EAAOW,iBAAiB,UAAW9B,KAAKE,SACpC2B,GACFV,EAAOe,aAAa,cAAe,GAEvC,CACAN,eAAAA,CAAgBT,GACdA,EAAOgB,oBAAoB,UAAWnC,KAAKE,SAC3CiB,EAAOgB,oBAAoB,OAAQnC,KAAKG,QACxCgB,EAAOgB,oBAAoB,UAAWnC,KAAKI,UAC3Ce,EAAOgB,oBAAoB,UAAWnC,KAAKK,aAC3Cc,EAAOgB,oBAAoB,QAASnC,KAAKM,SACzCa,EAAOgB,oBAAoB,SAAUnC,KAAKc,QAC1CK,EAAOgB,oBAAoB,QAASnC,KAAKO,SACzCY,EAAOgB,oBAAoB,QAASnC,KAAKQ,SACzCW,EAAOgB,oBAAoB,aAAcnC,KAAK+B,sBAC9CZ,EAAOgB,oBAAoB,wBAAyBnC,KAAKY,aACzDO,EAAOgB,oBAAoB,wBAAyBnC,KAAKgC,cACzDb,EAAOgB,oBAAoB,UAAWnC,KAAKE,QAC7C,CACA,UAAMkC,CAAKV,GACT,IAAIW,EACJ,MAAM,QAAE7B,EAAO,OAAE8B,GAAWtC,KAAKC,MACjC,KAA0C,OAAnCoC,EAAKE,WAAWC,qBAA0B,EAASH,EAAGjE,IAAI,eAC/D,IACE,MAAMqE,EAhFE,2EAgFeC,QAAQ,UAAWJ,EAAOK,eAC3CC,OAEJ,GAAGH,KAELzC,KAAKC,MAAM4C,UACb,CAAE,MAAOC,GACPtC,EAAQsC,EACV,CAEF,MAAO,CAAEC,GAAMrB,EAAIsB,MAAMtD,EAAgBuD,eACzCjD,KAAKmB,OAAOK,WAAauB,CAC3B,CACAG,IAAAA,GACE,MAAMC,EAAUnD,KAAKmB,OAAO+B,OACxBC,GACFA,EAAQC,MAAMpD,KAAKC,MAAMO,QAE7B,CACA6C,KAAAA,GACErD,KAAKmB,OAAOkC,OACd,CACAC,IAAAA,GACEtD,KAAKmB,OAAOK,WAAa,IAC3B,CACA+B,MAAAA,CAAOC,GAA6B,IAApBC,IAAW1D,UAAA2D,OAAA,QAAAC,IAAA5D,UAAA,KAAAA,UAAA,GACzBC,KAAKmB,OAAOJ,YAAcyC,EACrBC,GACHzD,KAAKqD,OAET,CACAO,SAAAA,CAAUC,GACR7D,KAAKmB,OAAO2C,OAASD,CACvB,CACAE,SAAAA,GACM/D,KAAKmB,OAAO6C,yBAA2BC,SAASC,0BAA4BlE,KAAKmB,QACnFnB,KAAKmB,OAAO6C,yBAEhB,CACAG,UAAAA,GACMF,SAASG,sBAAwBH,SAASC,0BAA4BlE,KAAKmB,QAC7E8C,SAASG,sBAEb,CACAC,eAAAA,CAAgBC,GACd,IACEtE,KAAKmB,OAAOR,aAAe2D,CAC7B,CAAE,MAAOxB,GACP9C,KAAKC,MAAMO,QAAQsC,EACrB,CACF,CACA7B,WAAAA,GACE,IAAKjB,KAAKmB,OACR,OAAO,KACT,MAAM,SAAEH,EAAQ,SAAEuD,GAAavE,KAAKmB,OACpC,OAAIH,IAAawD,KAAYD,EAASb,OAAS,EACtCa,EAASE,IAAIF,EAASb,OAAS,GAEjC1C,CACT,CACA0D,cAAAA,GACE,OAAK1E,KAAKmB,OAEHnB,KAAKmB,OAAOJ,YADV,IAEX,CACA4D,gBAAAA,GACE,IAAK3E,KAAKmB,OACR,OAAO,KACT,MAAM,SAAEyD,GAAa5E,KAAKmB,OAC1B,GAAwB,IAApByD,EAASlB,OACX,OAAO,EAET,MAAMe,EAAMG,EAASH,IAAIG,EAASlB,OAAS,GACrC1C,EAAWhB,KAAKiB,cACtB,OAAIwD,EAAMzD,EACDA,EAEFyD,CACT,CACAhD,aAAAA,CAAcC,GACZ,MAAO,CAAEqB,GAAMrB,EAAIsB,MAAMtD,EAAgBuD,eACzC,OAAOF,CACT,CACA8B,MAAAA,GACE,MAAM,IAAEnD,EAAG,QAAEoD,EAAO,KAAEC,EAAI,SAAEC,EAAQ,MAAE5D,EAAK,OAAEkB,EAAM,MAAE2C,EAAK,OAAEC,GAAWlF,KAAKC,MACtEkF,EAAQ,CACZF,MAAiB,SAAVA,EAAmBA,EAAQ,OAClCC,OAAmB,SAAXA,EAAoBA,EAAS,QAKvC,OAHiB,IAAbF,IACFG,EAAM,cAAgB,QAED9F,EAAaJ,QAAQmG,cAC1C,aACA,CACEC,IAAKrF,KAAKqF,IACV,cAAerF,KAAKyB,cAAcC,GAClCyD,QACAG,QAAS,OACTC,SAAUT,QAAW,EACrB1D,MAAOA,EAAQ,QAAK,EACpB2D,KAAMA,EAAO,QAAK,KACfzC,EAAOkD,YAGhB,EAEFlH,EAAcY,EAAK,cAAe,OAClCZ,EAAcY,EAAK,UAAWQ,EAAgB+F,QAAQC,I", "sources": ["../node_modules/react-player/lib/players/Mux.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Mux_exports = {};\n__export(Mux_exports, {\n  default: () => Mux\n});\nmodule.exports = __toCommonJS(Mux_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://cdn.jsdelivr.net/npm/@mux/mux-player@VERSION/dist/mux-player.mjs\";\nclass Mux extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onReady\", (...args) => this.props.onReady(...args));\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onBuffer\", (...args) => this.props.onBuffer(...args));\n    __publicField(this, \"onBufferEnd\", (...args) => this.props.onBufferEnd(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onError\", (...args) => this.props.onError(...args));\n    __publicField(this, \"onPlayBackRateChange\", (event) => this.props.onPlaybackRateChange(event.target.playbackRate));\n    __publicField(this, \"onEnablePIP\", (...args) => this.props.onEnablePIP(...args));\n    __publicField(this, \"onSeek\", (e) => {\n      this.props.onSeek(e.target.currentTime);\n    });\n    __publicField(this, \"onDurationChange\", () => {\n      const duration = this.getDuration();\n      this.props.onDuration(duration);\n    });\n    __publicField(this, \"mute\", () => {\n      this.player.muted = true;\n    });\n    __publicField(this, \"unmute\", () => {\n      this.player.muted = false;\n    });\n    __publicField(this, \"ref\", (player) => {\n      this.player = player;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n    this.addListeners(this.player);\n    const playbackId = this.getPlaybackId(this.props.url);\n    if (playbackId) {\n      this.player.playbackId = playbackId;\n    }\n  }\n  componentWillUnmount() {\n    this.player.playbackId = null;\n    this.removeListeners(this.player);\n  }\n  addListeners(player) {\n    const { playsinline } = this.props;\n    player.addEventListener(\"play\", this.onPlay);\n    player.addEventListener(\"waiting\", this.onBuffer);\n    player.addEventListener(\"playing\", this.onBufferEnd);\n    player.addEventListener(\"pause\", this.onPause);\n    player.addEventListener(\"seeked\", this.onSeek);\n    player.addEventListener(\"ended\", this.onEnded);\n    player.addEventListener(\"error\", this.onError);\n    player.addEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.addEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.addEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.addEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    player.addEventListener(\"canplay\", this.onReady);\n    if (playsinline) {\n      player.setAttribute(\"playsinline\", \"\");\n    }\n  }\n  removeListeners(player) {\n    player.removeEventListener(\"canplay\", this.onReady);\n    player.removeEventListener(\"play\", this.onPlay);\n    player.removeEventListener(\"waiting\", this.onBuffer);\n    player.removeEventListener(\"playing\", this.onBufferEnd);\n    player.removeEventListener(\"pause\", this.onPause);\n    player.removeEventListener(\"seeked\", this.onSeek);\n    player.removeEventListener(\"ended\", this.onEnded);\n    player.removeEventListener(\"error\", this.onError);\n    player.removeEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.removeEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.removeEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.removeEventListener(\"canplay\", this.onReady);\n  }\n  async load(url) {\n    var _a;\n    const { onError, config } = this.props;\n    if (!((_a = globalThis.customElements) == null ? void 0 : _a.get(\"mux-player\"))) {\n      try {\n        const sdkUrl = SDK_URL.replace(\"VERSION\", config.version);\n        await import(\n          /* webpackIgnore: true */\n          `${sdkUrl}`\n        );\n        this.props.onLoaded();\n      } catch (error) {\n        onError(error);\n      }\n    }\n    const [, id] = url.match(import_patterns.MATCH_URL_MUX);\n    this.player.playbackId = id;\n  }\n  play() {\n    const promise = this.player.play();\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.player.pause();\n  }\n  stop() {\n    this.player.playbackId = null;\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.player.currentTime = seconds;\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.player.volume = fraction;\n  }\n  enablePIP() {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture();\n    }\n  }\n  disablePIP() {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture();\n    }\n  }\n  setPlaybackRate(rate) {\n    try {\n      this.player.playbackRate = rate;\n    } catch (error) {\n      this.props.onError(error);\n    }\n  }\n  getDuration() {\n    if (!this.player)\n      return null;\n    const { duration, seekable } = this.player;\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1);\n    }\n    return duration;\n  }\n  getCurrentTime() {\n    if (!this.player)\n      return null;\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    if (!this.player)\n      return null;\n    const { buffered } = this.player;\n    if (buffered.length === 0) {\n      return 0;\n    }\n    const end = buffered.end(buffered.length - 1);\n    const duration = this.getDuration();\n    if (end > duration) {\n      return duration;\n    }\n    return end;\n  }\n  getPlaybackId(url) {\n    const [, id] = url.match(import_patterns.MATCH_URL_MUX);\n    return id;\n  }\n  render() {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props;\n    const style = {\n      width: width === \"auto\" ? width : \"100%\",\n      height: height === \"auto\" ? height : \"100%\"\n    };\n    if (controls === false) {\n      style[\"--controls\"] = \"none\";\n    }\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"mux-player\",\n      {\n        ref: this.ref,\n        \"playback-id\": this.getPlaybackId(url),\n        style,\n        preload: \"auto\",\n        autoPlay: playing || void 0,\n        muted: muted ? \"\" : void 0,\n        loop: loop ? \"\" : void 0,\n        ...config.attributes\n      }\n    );\n  }\n}\n__publicField(Mux, \"displayName\", \"Mux\");\n__publicField(Mux, \"canPlay\", import_patterns.canPlay.mux);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "Mux_exports", "__export", "target", "all", "name", "default", "<PERSON><PERSON>", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_patterns", "Component", "constructor", "_this", "super", "arguments", "this", "props", "onReady", "onPlay", "onBuffer", "onBufferEnd", "onPause", "onEnded", "onError", "event", "onPlaybackRateChange", "playbackRate", "onEnablePIP", "e", "onSeek", "currentTime", "duration", "getDuration", "onDuration", "player", "muted", "componentDidMount", "onMount", "addListeners", "playbackId", "getPlaybackId", "url", "componentWillUnmount", "removeListeners", "playsinline", "addEventListener", "onPlayBackRateChange", "onDisablePIP", "onPresentationModeChange", "setAttribute", "removeEventListener", "load", "_a", "config", "globalThis", "customElements", "sdkUrl", "replace", "version", "import", "onLoaded", "error", "id", "match", "MATCH_URL_MUX", "play", "promise", "catch", "pause", "stop", "seekTo", "seconds", "keepPlaying", "length", "undefined", "setVolume", "fraction", "volume", "enablePIP", "requestPictureInPicture", "document", "pictureInPictureElement", "disablePIP", "exitPictureInPicture", "setPlaybackRate", "rate", "seekable", "Infinity", "end", "getCurrentTime", "getSecondsLoaded", "buffered", "render", "playing", "loop", "controls", "width", "height", "style", "createElement", "ref", "preload", "autoPlay", "attributes", "canPlay", "mux"], "sourceRoot": ""}