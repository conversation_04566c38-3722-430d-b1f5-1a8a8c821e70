{"version": 3, "file": "static/js/reactPlayerFacebook.3ac90a45.chunk.js", "mappings": "kFAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAmB,CAAC,EAzBTC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAkB,CACzBK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,OAC/BC,EAAeD,EAAQ,MACvBE,EAAkBF,EAAQ,MAC9B,MAAMG,EAAU,4CAEVC,EAAmB,cAEzB,MAAMX,UAAiBG,EAAaS,UAClCC,WAAAA,GACEC,SAASC,WACT3B,EAAc4B,KAAM,aAAcR,EAAaS,YAC/C7B,EAAc4B,KAAM,WAAYA,KAAKE,MAAMC,OAAOC,UAAY,oBAAsB,EAAIZ,EAAaa,mBACrGjC,EAAc4B,KAAM,QAAQ,KAC1BA,KAAKC,WAAW,OAAO,IAEzB7B,EAAc4B,KAAM,UAAU,KAC5BA,KAAKC,WAAW,SAAS,GAE7B,CACAK,iBAAAA,GACEN,KAAKE,MAAMK,SAAWP,KAAKE,MAAMK,QAAQP,KAC3C,CACAQ,IAAAA,CAAKC,EAAKC,GACJA,GACF,EAAIlB,EAAamB,QAAQjB,EApBZ,KAoBiCC,GAAkBiB,MAAMC,GAAOA,EAAGC,MAAMC,WAGxF,EAAIvB,EAAamB,QAAQjB,EAvBV,KAuB+BC,GAAkBiB,MAAMC,IACpEA,EAAGG,KAAK,CACNC,MAAOjB,KAAKE,MAAMC,OAAOc,MACzBC,OAAO,EACPC,QAASnB,KAAKE,MAAMC,OAAOgB,UAE7BN,EAAGO,MAAMC,UAAU,gBAAiBC,IAClCtB,KAAKE,MAAMqB,UAAU,IAEvBV,EAAGO,MAAMC,UAAU,eAAgBC,IAChB,UAAbA,EAAIE,MAAoBF,EAAIG,KAAOzB,KAAK0B,WAC1C1B,KAAK2B,OAASL,EAAIM,SAClB5B,KAAK2B,OAAON,UAAU,iBAAkBrB,KAAKE,MAAM2B,QACnD7B,KAAK2B,OAAON,UAAU,SAAUrB,KAAKE,MAAM4B,SAC3C9B,KAAK2B,OAAON,UAAU,kBAAmBrB,KAAKE,MAAM6B,SACpD/B,KAAK2B,OAAON,UAAU,mBAAoBrB,KAAKE,MAAM8B,UACrDhC,KAAK2B,OAAON,UAAU,oBAAqBrB,KAAKE,MAAM+B,aACtDjC,KAAK2B,OAAON,UAAU,QAASrB,KAAKE,MAAMgC,SACtClC,KAAKE,MAAMiC,MACbnC,KAAKC,WAAW,QAEhBD,KAAKC,WAAW,UAElBD,KAAKE,MAAMkC,UACXC,SAASC,eAAetC,KAAK0B,UAAUa,cAAc,UAAUC,MAAMC,WAAa,UACpF,GACA,GAEN,CACAC,IAAAA,GACE1C,KAAKC,WAAW,OAClB,CACA0C,KAAAA,GACE3C,KAAKC,WAAW,QAClB,CACA2C,IAAAA,GACA,CACAC,MAAAA,CAAOC,GAA6B,IAApBC,IAAWhD,UAAAiD,OAAA,QAAAC,IAAAlD,UAAA,KAAAA,UAAA,GACzBC,KAAKC,WAAW,OAAQ6C,GACnBC,GACH/C,KAAK2C,OAET,CACAO,SAAAA,CAAUC,GACRnD,KAAKC,WAAW,YAAakD,EAC/B,CACAC,WAAAA,GACE,OAAOpD,KAAKC,WAAW,cACzB,CACAoD,cAAAA,GACE,OAAOrD,KAAKC,WAAW,qBACzB,CACAqD,gBAAAA,GACE,OAAO,IACT,CACAC,MAAAA,GACE,MAAM,WAAEC,GAAexD,KAAKE,MAAMC,OAKlC,OAAuBhB,EAAaJ,QAAQ0E,cAC1C,MACA,CACEjB,MAPU,CACZkB,MAAO,OACPC,OAAQ,QAMNlC,GAAIzB,KAAK0B,SACTkC,UAAW,WACX,YAAa5D,KAAKE,MAAMO,IACxB,gBAAiBT,KAAKE,MAAM2D,QAAU,OAAS,QAC/C,uBAAwB,OACxB,gBAAiB7D,KAAKE,MAAM4D,SAAW,OAAS,WAC7CN,GAGT,EAEFpF,EAAcY,EAAU,cAAe,YACvCZ,EAAcY,EAAU,UAAWS,EAAgBsE,QAAQC,UAC3D5F,EAAcY,EAAU,eAAe,E", "sources": ["../node_modules/react-player/lib/players/Facebook.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Facebook_exports = {};\n__export(Facebook_exports, {\n  default: () => Facebook\n});\nmodule.exports = __toCommonJS(Facebook_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://connect.facebook.net/en_US/sdk.js\";\nconst SDK_GLOBAL = \"FB\";\nconst SDK_GLOBAL_READY = \"fbAsyncInit\";\nconst PLAYER_ID_PREFIX = \"facebook-player-\";\nclass Facebook extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    if (isReady) {\n      (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then((FB) => FB.XFBML.parse());\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then((FB) => {\n      FB.init({\n        appId: this.props.config.appId,\n        xfbml: true,\n        version: this.props.config.version\n      });\n      FB.Event.subscribe(\"xfbml.render\", (msg) => {\n        this.props.onLoaded();\n      });\n      FB.Event.subscribe(\"xfbml.ready\", (msg) => {\n        if (msg.type === \"video\" && msg.id === this.playerID) {\n          this.player = msg.instance;\n          this.player.subscribe(\"startedPlaying\", this.props.onPlay);\n          this.player.subscribe(\"paused\", this.props.onPause);\n          this.player.subscribe(\"finishedPlaying\", this.props.onEnded);\n          this.player.subscribe(\"startedBuffering\", this.props.onBuffer);\n          this.player.subscribe(\"finishedBuffering\", this.props.onBufferEnd);\n          this.player.subscribe(\"error\", this.props.onError);\n          if (this.props.muted) {\n            this.callPlayer(\"mute\");\n          } else {\n            this.callPlayer(\"unmute\");\n          }\n          this.props.onReady();\n          document.getElementById(this.playerID).querySelector(\"iframe\").style.visibility = \"visible\";\n        }\n      });\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  getDuration() {\n    return this.callPlayer(\"getDuration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"getCurrentPosition\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { attributes } = this.props.config;\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        style,\n        id: this.playerID,\n        className: \"fb-video\",\n        \"data-href\": this.props.url,\n        \"data-autoplay\": this.props.playing ? \"true\" : \"false\",\n        \"data-allowfullscreen\": \"true\",\n        \"data-controls\": this.props.controls ? \"true\" : \"false\",\n        ...attributes\n      }\n    );\n  }\n}\n__publicField(Facebook, \"displayName\", \"Facebook\");\n__publicField(Facebook, \"canPlay\", import_patterns.canPlay.facebook);\n__publicField(Facebook, \"loopOnEnded\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "Facebook_exports", "__export", "target", "all", "name", "default", "Facebook", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "SDK_URL", "SDK_GLOBAL_READY", "Component", "constructor", "super", "arguments", "this", "callPlayer", "props", "config", "playerId", "randomString", "componentDidMount", "onMount", "load", "url", "isReady", "getSDK", "then", "FB", "XFBML", "parse", "init", "appId", "xfbml", "version", "Event", "subscribe", "msg", "onLoaded", "type", "id", "playerID", "player", "instance", "onPlay", "onPause", "onEnded", "onBuffer", "onBufferEnd", "onError", "muted", "onReady", "document", "getElementById", "querySelector", "style", "visibility", "play", "pause", "stop", "seekTo", "seconds", "keepPlaying", "length", "undefined", "setVolume", "fraction", "getDuration", "getCurrentTime", "getSecondsLoaded", "render", "attributes", "createElement", "width", "height", "className", "playing", "controls", "canPlay", "facebook"], "sourceRoot": ""}