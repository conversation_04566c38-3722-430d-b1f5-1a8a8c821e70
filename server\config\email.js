const nodemailer = require('nodemailer');
require('dotenv').config();

// Import SendGrid if available
let sgMail;
try {
  sgMail = require('@sendgrid/mail');
  if (process.env.SENDGRID_API_KEY) {
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
    console.log('✅ SendGrid API key configured successfully');
  }
} catch (error) {
  console.warn('⚠️ SendGrid package not available, using SMTP only');
}

// Email configuration (SendGrid Only)
const emailConfig = {
  // SendGrid configuration
  sendgrid: {
    apiKey: process.env.SENDGRID_API_KEY || '*********************************************************************',
    fromName: process.env.EMAIL_FROM_NAME || 'Allemni online',
    fromAddress: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'
  },

  // SMTP configuration (SendGrid SMTP as fallback)
  smtp: {
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    user: 'apikey',
    password: process.env.SENDGRID_API_KEY || '*********************************************************************'
  }
};

// Create SendGrid transporter
const createSendGridTransporter = () => {
  return nodemailer.createTransport({
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    auth: {
      user: 'apikey',
      pass: emailConfig.sendgrid.apiKey
    }
  });
};



// Create general SMTP transporter
const createSMTPTransporter = () => {
  return nodemailer.createTransport({
    host: emailConfig.smtp.host,
    port: emailConfig.smtp.port,
    secure: emailConfig.smtp.secure,
    auth: {
      user: emailConfig.smtp.user,
      pass: emailConfig.smtp.password
    }
  });
};

// Verify transporter configuration
const verifyTransporter = async (transporter, name) => {
  try {
    await transporter.verify();
    console.log(`✅ ${name} transporter is ready to send emails`);
    return true;
  } catch (error) {
    console.error(`❌ ${name} transporter configuration error:`, error.message);
    // For SendGrid SMTP, verification might fail but sending still works
    if (name === 'SendGrid' || name === 'SMTP') {
      console.log(`⚠️ ${name} verification failed but will still be available for sending`);
      return true; // Allow SendGrid to be used even if verification fails
    }
    return false;
  }
};

// Initialize and verify transporters
const initializeTransporters = async () => {
  const transporters = {};

  console.log('🔧 Initializing email transporters...');
  console.log('Environment SENDGRID_API_KEY:', process.env.SENDGRID_API_KEY ? 'Present' : 'Missing');
  console.log('Config SENDGRID_API_KEY:', emailConfig.sendgrid.apiKey ? 'Present' : 'Missing');
  console.log('SendGrid API Key available:', !!(emailConfig.sendgrid.apiKey && emailConfig.sendgrid.apiKey !== 'your-sendgrid-api-key'));

  // Initialize SendGrid if API key is available
  if (emailConfig.sendgrid.apiKey && emailConfig.sendgrid.apiKey !== 'your-sendgrid-api-key') {
    try {
      transporters.sendgrid = createSendGridTransporter();
      const verified = await verifyTransporter(transporters.sendgrid, 'SendGrid');
      if (!verified) {
        console.log('⚠️ SendGrid verification failed, but keeping transporter for fallback');
      }
    } catch (error) {
      console.error('❌ Failed to create SendGrid transporter:', error.message);
    }
  }

  // Initialize SMTP as fallback only if SendGrid API key is available
  if (emailConfig.sendgrid.apiKey && emailConfig.sendgrid.apiKey !== 'your-sendgrid-api-key' && !transporters.sendgrid) {
    try {
      transporters.smtp = createSMTPTransporter();
      await verifyTransporter(transporters.smtp, 'SMTP');
    } catch (error) {
      console.error('❌ Failed to create SMTP transporter:', error.message);
    }
  }

  console.log('📊 Available transporters:', Object.keys(transporters));
  return transporters;
};

// Get the best available transporter
const getBestTransporter = (transporters, preferredService = 'sendgrid') => {
  console.log('🔍 Looking for best transporter...');
  console.log('Available transporters:', Object.keys(transporters));

  // Always prefer SendGrid if available (most reliable)
  if (transporters.sendgrid) {
    console.log('✅ Using SendGrid service');
    return { transporter: transporters.sendgrid, service: 'sendgrid' };
  }

  // Use SMTP as fallback if it has proper credentials (SendGrid API key)
  if (transporters.smtp && emailConfig.sendgrid.apiKey && emailConfig.sendgrid.apiKey !== 'your-sendgrid-api-key') {
    console.log('✅ Using SMTP as fallback service');
    return { transporter: transporters.smtp, service: 'smtp' };
  }

  // Emergency fallback: create SendGrid transporter if API key exists
  if (emailConfig.sendgrid.apiKey && emailConfig.sendgrid.apiKey !== 'your-sendgrid-api-key') {
    console.log('🚨 Creating emergency SendGrid transporter');
    const emergencyTransporter = createSendGridTransporter();
    return { transporter: emergencyTransporter, service: 'sendgrid-emergency' };
  }

  // Last resort: hardcoded SendGrid transporter
  console.log('🆘 Creating hardcoded SendGrid transporter as last resort');
  const hardcodedTransporter = nodemailer.createTransport({
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    auth: {
      user: 'apikey',
      pass: '*********************************************************************'
    }
  });
  return { transporter: hardcodedTransporter, service: 'sendgrid-hardcoded' };
};

// Email service status
const getEmailServiceStatus = () => {
  return {
    sendgrid: {
      configured: !!(emailConfig.sendgrid.apiKey && emailConfig.sendgrid.apiKey !== 'your-sendgrid-api-key'),
      apiKey: emailConfig.sendgrid.apiKey ? '***configured***' : 'not configured',
      fromName: emailConfig.sendgrid.fromName,
      fromAddress: emailConfig.sendgrid.fromAddress
    },
    smtp: {
      configured: !!(emailConfig.sendgrid.apiKey && emailConfig.sendgrid.apiKey !== 'your-sendgrid-api-key'),
      host: emailConfig.smtp.host,
      port: emailConfig.smtp.port
    }
  };
};

module.exports = {
  emailConfig,
  sgMail,
  createSendGridTransporter,
  createSMTPTransporter,
  initializeTransporters,
  getBestTransporter,
  getEmailServiceStatus,
  verifyTransporter
};
