{"version": 3, "file": "static/js/reactPlayerFilePlayer.f7df6bb1.chunk.js", "mappings": "kFAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAqB,CAAC,EAzBXC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAoB,CAC3BK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,OAC/BC,EAAeD,EAAQ,MACvBE,EAAkBF,EAAQ,MAC9B,MAAMG,EAAqC,qBAAdC,UACvBC,EAAcF,GAAwC,aAAvBC,UAAUE,UAA2BF,UAAUG,eAAiB,EAC/FC,EAASL,IAAkB,mBAAmBM,KAAKL,UAAUM,YAAcL,KAAiBM,OAAOC,SACnGC,EAAYV,GAAiB,iCAAiCM,KAAKL,UAAUM,aAAeC,OAAOC,SAOnGE,EAAoB,wBACpBC,EAA0B,sDAEhC,MAAMtB,UAAmBG,EAAaoB,UACpCC,WAAAA,GAAc,IAAAC,EACZC,SAASC,WAAUF,EAAAG,KAEnBxC,EAAcwC,KAAM,WAAW,kBAAaH,EAAKI,MAAMC,WAAQH,UAAQ,IACvEvC,EAAcwC,KAAM,UAAU,kBAAaH,EAAKI,MAAME,UAAOJ,UAAQ,IACrEvC,EAAcwC,KAAM,YAAY,kBAAaH,EAAKI,MAAMG,YAASL,UAAQ,IACzEvC,EAAcwC,KAAM,eAAe,kBAAaH,EAAKI,MAAMI,eAAYN,UAAQ,IAC/EvC,EAAcwC,KAAM,WAAW,kBAAaH,EAAKI,MAAMK,WAAQP,UAAQ,IACvEvC,EAAcwC,KAAM,WAAW,kBAAaH,EAAKI,MAAMM,WAAQR,UAAQ,IACvEvC,EAAcwC,KAAM,WAAW,kBAAaH,EAAKI,MAAMO,WAAQT,UAAQ,IACvEvC,EAAcwC,KAAM,wBAAyBS,GAAUT,KAAKC,MAAMS,qBAAqBD,EAAMzC,OAAO2C,gBACpGnD,EAAcwC,KAAM,eAAe,kBAAaH,EAAKI,MAAMW,eAAYb,UAAQ,IAC/EvC,EAAcwC,KAAM,gBAAiBa,IACnC,MAAM,aAAEC,EAAY,QAAEC,GAAYf,KAAKC,MACvCa,EAAaD,GACTE,GACFf,KAAKgB,MACP,IAEFxD,EAAcwC,KAAM,4BAA6Ba,IAC/C,GAAIb,KAAKiB,SAAU,EAAIrC,EAAasC,gCAAgClB,KAAKiB,QAAS,CAChF,MAAM,uBAAEE,GAA2BnB,KAAKiB,OACT,uBAA3BE,EACFnB,KAAKY,YAAYC,GACmB,WAA3BM,GACTnB,KAAKc,aAAaD,EAEtB,KAEFrD,EAAcwC,KAAM,UAAWa,IAC7Bb,KAAKC,MAAMmB,OAAOP,EAAE7C,OAAOqD,YAAY,IAEzC7D,EAAcwC,KAAM,QAAQ,KAC1BA,KAAKiB,OAAOK,OAAQ,CAAI,IAE1B9D,EAAcwC,KAAM,UAAU,KAC5BA,KAAKiB,OAAOK,OAAQ,CAAK,IAE3B9D,EAAcwC,KAAM,uBAAuB,CAACuB,EAAQC,IAC5B,kBAAXD,EACchD,EAAaJ,QAAQsD,cAAc,SAAU,CAAErE,IAAKoE,EAAOE,IAAKH,IAElEhD,EAAaJ,QAAQsD,cAAc,SAAU,CAAErE,IAAKoE,KAAUD,MAEvF/D,EAAcwC,KAAM,eAAe,CAAC2B,EAAOH,IAClBjD,EAAaJ,QAAQsD,cAAc,QAAS,CAAErE,IAAKoE,KAAUG,MAEtFnE,EAAcwC,KAAM,OAAQiB,IACtBjB,KAAKiB,SACPjB,KAAK4B,WAAa5B,KAAKiB,QAEzBjB,KAAKiB,OAASA,CAAM,GAExB,CACAY,iBAAAA,GACE7B,KAAKC,MAAM6B,SAAW9B,KAAKC,MAAM6B,QAAQ9B,MACzCA,KAAK+B,aAAa/B,KAAKiB,QACvB,MAAMS,EAAM1B,KAAKgC,UAAUhC,KAAKC,MAAMgC,KAClCP,IACF1B,KAAKiB,OAAOS,IAAMA,IAEhBvC,GAAUa,KAAKC,MAAMiC,OAAOC,kBAC9BnC,KAAKiB,OAAOmB,MAEhB,CACAC,kBAAAA,CAAmBC,GACbtC,KAAKuC,eAAevC,KAAKC,SAAWD,KAAKuC,eAAeD,KAC1DtC,KAAKwC,gBAAgBxC,KAAK4B,WAAYU,EAAUL,KAChDjC,KAAK+B,aAAa/B,KAAKiB,SAErBjB,KAAKC,MAAMgC,MAAQK,EAAUL,MAAQ,EAAIrD,EAAa6D,eAAezC,KAAKC,MAAMgC,MAAUjC,KAAKC,MAAMgC,eAAeS,QACtH1C,KAAKiB,OAAO0B,UAAY,KAE5B,CACAC,oBAAAA,GACE5C,KAAKiB,OAAO4B,gBAAgB,OAC5B7C,KAAKwC,gBAAgBxC,KAAKiB,QACtBjB,KAAK8C,KACP9C,KAAK8C,IAAIC,SAEb,CACAhB,YAAAA,CAAad,GACX,MAAM,IAAEgB,EAAG,YAAEe,GAAgBhD,KAAKC,MAClCgB,EAAOgC,iBAAiB,OAAQjD,KAAKG,QACrCc,EAAOgC,iBAAiB,UAAWjD,KAAKI,UACxCa,EAAOgC,iBAAiB,UAAWjD,KAAKK,aACxCY,EAAOgC,iBAAiB,QAASjD,KAAKM,SACtCW,EAAOgC,iBAAiB,SAAUjD,KAAKoB,QACvCH,EAAOgC,iBAAiB,QAASjD,KAAKO,SACtCU,EAAOgC,iBAAiB,QAASjD,KAAKQ,SACtCS,EAAOgC,iBAAiB,aAAcjD,KAAKkD,sBAC3CjC,EAAOgC,iBAAiB,wBAAyBjD,KAAKY,aACtDK,EAAOgC,iBAAiB,wBAAyBjD,KAAKc,cACtDG,EAAOgC,iBAAiB,gCAAiCjD,KAAKmD,0BACzDnD,KAAKoD,aAAanB,IACrBhB,EAAOgC,iBAAiB,UAAWjD,KAAKE,SAEtC8C,IACF/B,EAAOoC,aAAa,cAAe,IACnCpC,EAAOoC,aAAa,qBAAsB,IAC1CpC,EAAOoC,aAAa,iBAAkB,IAE1C,CACAb,eAAAA,CAAgBvB,EAAQgB,GACtBhB,EAAOqC,oBAAoB,UAAWtD,KAAKE,SAC3Ce,EAAOqC,oBAAoB,OAAQtD,KAAKG,QACxCc,EAAOqC,oBAAoB,UAAWtD,KAAKI,UAC3Ca,EAAOqC,oBAAoB,UAAWtD,KAAKK,aAC3CY,EAAOqC,oBAAoB,QAAStD,KAAKM,SACzCW,EAAOqC,oBAAoB,SAAUtD,KAAKoB,QAC1CH,EAAOqC,oBAAoB,QAAStD,KAAKO,SACzCU,EAAOqC,oBAAoB,QAAStD,KAAKQ,SACzCS,EAAOqC,oBAAoB,aAActD,KAAKkD,sBAC9CjC,EAAOqC,oBAAoB,wBAAyBtD,KAAKY,aACzDK,EAAOqC,oBAAoB,wBAAyBtD,KAAKc,cACzDG,EAAOqC,oBAAoB,gCAAiCtD,KAAKmD,0BAC5DnD,KAAKoD,aAAanB,IACrBhB,EAAOqC,oBAAoB,UAAWtD,KAAKE,QAE/C,CACAqC,cAAAA,CAAetC,GACb,OAAIA,EAAMiC,OAAOqB,cAGbtD,EAAMiC,OAAOsB,WAAWC,SAGrB5E,EAAgB6E,iBAAiBtE,KAAKa,EAAMgC,MAAQhC,EAAMiC,OAAOyB,YAC1E,CACAP,YAAAA,CAAanB,GACX,SAAIzC,GAAaQ,KAAKC,MAAMiC,OAAO0B,gBAAkB5D,KAAKC,MAAMiC,OAAO2B,YAGnE1E,IAAUa,KAAKC,MAAMiC,OAAOC,kBAGzBtD,EAAgBiF,eAAe1E,KAAK6C,IAAQvC,EAAwBN,KAAK6C,GAClF,CACA8B,aAAAA,CAAc9B,GACZ,OAAOpD,EAAgBmF,gBAAgB5E,KAAK6C,IAAQjC,KAAKC,MAAMiC,OAAO+B,SACxE,CACAC,YAAAA,CAAajC,GACX,OAAOpD,EAAgBsF,eAAe/E,KAAK6C,IAAQjC,KAAKC,MAAMiC,OAAOkC,QACvE,CACAhC,IAAAA,CAAKH,GACH,MAAM,WAAEoC,EAAU,WAAEC,EAAU,YAAEC,EAAW,WAAEC,GAAexE,KAAKC,MAAMiC,OAkDvE,GAjDIlC,KAAK8C,KACP9C,KAAK8C,IAAIC,UAEP/C,KAAKyE,MACPzE,KAAKyE,KAAKC,QAER1E,KAAKoD,aAAanB,KACpB,EAAIrD,EAAa+F,QAnKH,8DAmKuBC,QAAQ,UAAWP,GAlK3C,OAkKoEQ,MAAMC,IAQrF,GAPA9E,KAAK8C,IAAM,IAAIgC,EAAIR,GACnBtE,KAAK8C,IAAIiC,GAAGD,EAAIE,OAAOC,iBAAiB,KACtCjF,KAAKC,MAAMC,SAAS,IAEtBF,KAAK8C,IAAIiC,GAAGD,EAAIE,OAAOE,OAAO,CAACrE,EAAGsE,KAChCnF,KAAKC,MAAMO,QAAQK,EAAGsE,EAAMnF,KAAK8C,IAAKgC,EAAI,IAExCpF,EAAwBN,KAAK6C,GAAM,CACrC,MAAMmD,EAAKnD,EAAIoD,MAAM3F,GAAyB,GAC9CM,KAAK8C,IAAIwC,WArKe,qDAqKsBV,QAAQ,OAAQQ,GAChE,MACEpF,KAAK8C,IAAIwC,WAAWrD,GAEtBjC,KAAK8C,IAAIyC,YAAYvF,KAAKiB,QAC1BjB,KAAKC,MAAMuF,UAAU,IAGrBxF,KAAK+D,cAAc9B,KACrB,EAAIrD,EAAa+F,QApLF,wEAoLuBC,QAAQ,UAAWL,GAnL3C,UAmLsEM,MAAMY,IACxFzF,KAAKyE,KAAOgB,EAAOC,cAAcvJ,SACjC6D,KAAKyE,KAAKkB,WAAW3F,KAAKiB,OAAQgB,EAAKjC,KAAKC,MAAMc,SAClDf,KAAKyE,KAAKM,GAAG,QAAS/E,KAAKC,MAAMO,SAC7BoF,SAASrB,GAAe,EAC1BvE,KAAKyE,KAAKoB,WAAWC,wBAAuB,GAE5C9F,KAAKyE,KAAKsB,eAAe,CAAEC,MAAO,CAAEC,SAAUR,EAAOS,MAAMC,kBAE7DnG,KAAKC,MAAMuF,UAAU,IAGrBxF,KAAKkE,aAAajC,KACpB,EAAIrD,EAAa+F,QA/LH,8DA+LuBC,QAAQ,UAAWJ,GA9L3C,SA8LoEK,MAAMuB,IACrFpG,KAAKqG,IAAMD,EAAME,aAAa,CAAEC,KAAM,MAAOtE,QAC7CjC,KAAKqG,IAAIG,mBAAmBxG,KAAKiB,QACjCjB,KAAKqG,IAAItB,GAAGqB,EAAMpB,OAAOE,OAAO,CAACrE,EAAGsE,KAClCnF,KAAKC,MAAMO,QAAQK,EAAGsE,EAAMnF,KAAKqG,IAAKD,EAAM,IAE9CpG,KAAKqG,IAAIjE,OACTpC,KAAKC,MAAMuF,UAAU,IAGrBvD,aAAeS,MACjB1C,KAAKiB,OAAOmB,YACP,IAAI,EAAIxD,EAAa6D,eAAeR,GACzC,IACEjC,KAAKiB,OAAO0B,UAAYV,CAC1B,CAAE,MAAOpB,GACPb,KAAKiB,OAAOS,IAAMpC,OAAOmH,IAAIC,gBAAgBzE,EAC/C,CAEJ,CACAjB,IAAAA,GACE,MAAM2F,EAAU3G,KAAKiB,OAAOD,OACxB2F,GACFA,EAAQC,MAAM5G,KAAKC,MAAMO,QAE7B,CACAqG,KAAAA,GACE7G,KAAKiB,OAAO4F,OACd,CACAC,IAAAA,GACE9G,KAAKiB,OAAO4B,gBAAgB,OACxB7C,KAAKyE,MACPzE,KAAKyE,KAAKC,OAEd,CACAqC,MAAAA,CAAOC,GAA6B,IAApBC,IAAWlH,UAAAmH,OAAA,QAAAC,IAAApH,UAAA,KAAAA,UAAA,GACzBC,KAAKiB,OAAOI,YAAc2F,EACrBC,GACHjH,KAAK6G,OAET,CACAO,SAAAA,CAAUC,GACRrH,KAAKiB,OAAOqG,OAASD,CACvB,CACAE,SAAAA,GACMvH,KAAKiB,OAAOuG,yBAA2BC,SAASC,0BAA4B1H,KAAKiB,OACnFjB,KAAKiB,OAAOuG,2BACH,EAAI5I,EAAasC,gCAAgClB,KAAKiB,SAAkD,uBAAvCjB,KAAKiB,OAAOE,wBACtFnB,KAAKiB,OAAO0G,0BAA0B,qBAE1C,CACAC,UAAAA,GACMH,SAASI,sBAAwBJ,SAASC,0BAA4B1H,KAAKiB,OAC7EwG,SAASI,wBACA,EAAIjJ,EAAasC,gCAAgClB,KAAKiB,SAAkD,WAAvCjB,KAAKiB,OAAOE,wBACtFnB,KAAKiB,OAAO0G,0BAA0B,SAE1C,CACAG,eAAAA,CAAgBC,GACd,IACE/H,KAAKiB,OAAON,aAAeoH,CAC7B,CAAE,MAAOC,GACPhI,KAAKC,MAAMO,QAAQwH,EACrB,CACF,CACAC,WAAAA,GACE,IAAKjI,KAAKiB,OACR,OAAO,KACT,MAAM,SAAEiH,EAAQ,SAAEC,GAAanI,KAAKiB,OACpC,OAAIiH,IAAaE,KAAYD,EAASjB,OAAS,EACtCiB,EAASE,IAAIF,EAASjB,OAAS,GAEjCgB,CACT,CACAI,cAAAA,GACE,OAAKtI,KAAKiB,OAEHjB,KAAKiB,OAAOI,YADV,IAEX,CACAkH,gBAAAA,GACE,IAAKvI,KAAKiB,OACR,OAAO,KACT,MAAM,SAAEuH,GAAaxI,KAAKiB,OAC1B,GAAwB,IAApBuH,EAAStB,OACX,OAAO,EAET,MAAMmB,EAAMG,EAASH,IAAIG,EAAStB,OAAS,GACrCgB,EAAWlI,KAAKiI,cACtB,OAAII,EAAMH,EACDA,EAEFG,CACT,CACArG,SAAAA,CAAUC,GACR,MAAMwG,EAASzI,KAAKoD,aAAanB,GAC3ByG,EAAU1I,KAAK+D,cAAc9B,GAC7B0G,EAAS3I,KAAKkE,aAAajC,GACjC,KAAIA,aAAeS,QAAS,EAAI9D,EAAa6D,eAAeR,IAAQwG,GAAUC,GAAWC,GAGzF,OAAIlJ,EAAkBL,KAAK6C,GAClBA,EAAI2C,QAAQ,kBAAmB,6BAEjC3C,CACT,CACA2G,MAAAA,GACE,MAAM,IAAE3G,EAAG,QAAElB,EAAO,KAAE8H,EAAI,SAAEC,EAAQ,MAAExH,EAAK,OAAEY,EAAM,MAAE6G,EAAK,OAAEC,GAAWhJ,KAAKC,MAEtEgJ,EADWjJ,KAAKuC,eAAevC,KAAKC,OACf,QAAU,QAC/BiJ,EAAQ,CACZH,MAAiB,SAAVA,EAAmBA,EAAQ,OAClCC,OAAmB,SAAXA,EAAoBA,EAAS,QAEvC,OAAuBzK,EAAaJ,QAAQsD,cAC1CwH,EACA,CACEE,IAAKnJ,KAAKmJ,IACVzH,IAAK1B,KAAKgC,UAAUC,GACpBiH,QACAE,QAAS,OACTC,SAAUtI,QAAW,EACrB+H,WACAxH,QACAuH,UACG3G,EAAOsB,YAEZvB,aAAeS,OAAST,EAAIqH,IAAItJ,KAAKuJ,qBACrCrH,EAAOsH,OAAOF,IAAItJ,KAAKyJ,aAE3B,EAEFjM,EAAcY,EAAY,cAAe,cACzCZ,EAAcY,EAAY,UAAWS,EAAgB6K,QAAQC,K", "sources": ["../node_modules/react-player/lib/players/FilePlayer.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar FilePlayer_exports = {};\n__export(FilePlayer_exports, {\n  default: () => FilePlayer\n});\nmodule.exports = __toCommonJS(FilePlayer_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst HAS_NAVIGATOR = typeof navigator !== \"undefined\";\nconst IS_IPAD_PRO = HAS_NAVIGATOR && navigator.platform === \"MacIntel\" && navigator.maxTouchPoints > 1;\nconst IS_IOS = HAS_NAVIGATOR && (/iPad|iPhone|iPod/.test(navigator.userAgent) || IS_IPAD_PRO) && !window.MSStream;\nconst IS_SAFARI = HAS_NAVIGATOR && /^((?!chrome|android).)*safari/i.test(navigator.userAgent) && !window.MSStream;\nconst HLS_SDK_URL = \"https://cdn.jsdelivr.net/npm/hls.js@VERSION/dist/hls.min.js\";\nconst HLS_GLOBAL = \"Hls\";\nconst DASH_SDK_URL = \"https://cdnjs.cloudflare.com/ajax/libs/dashjs/VERSION/dash.all.min.js\";\nconst DASH_GLOBAL = \"dashjs\";\nconst FLV_SDK_URL = \"https://cdn.jsdelivr.net/npm/flv.js@VERSION/dist/flv.min.js\";\nconst FLV_GLOBAL = \"flvjs\";\nconst MATCH_DROPBOX_URL = /www\\.dropbox\\.com\\/.+/;\nconst MATCH_CLOUDFLARE_STREAM = /https:\\/\\/watch\\.cloudflarestream\\.com\\/([a-z0-9]+)/;\nconst REPLACE_CLOUDFLARE_STREAM = \"https://videodelivery.net/{id}/manifest/video.m3u8\";\nclass FilePlayer extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onReady\", (...args) => this.props.onReady(...args));\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onBuffer\", (...args) => this.props.onBuffer(...args));\n    __publicField(this, \"onBufferEnd\", (...args) => this.props.onBufferEnd(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onError\", (...args) => this.props.onError(...args));\n    __publicField(this, \"onPlayBackRateChange\", (event) => this.props.onPlaybackRateChange(event.target.playbackRate));\n    __publicField(this, \"onEnablePIP\", (...args) => this.props.onEnablePIP(...args));\n    __publicField(this, \"onDisablePIP\", (e) => {\n      const { onDisablePIP, playing } = this.props;\n      onDisablePIP(e);\n      if (playing) {\n        this.play();\n      }\n    });\n    __publicField(this, \"onPresentationModeChange\", (e) => {\n      if (this.player && (0, import_utils.supportsWebKitPresentationMode)(this.player)) {\n        const { webkitPresentationMode } = this.player;\n        if (webkitPresentationMode === \"picture-in-picture\") {\n          this.onEnablePIP(e);\n        } else if (webkitPresentationMode === \"inline\") {\n          this.onDisablePIP(e);\n        }\n      }\n    });\n    __publicField(this, \"onSeek\", (e) => {\n      this.props.onSeek(e.target.currentTime);\n    });\n    __publicField(this, \"mute\", () => {\n      this.player.muted = true;\n    });\n    __publicField(this, \"unmute\", () => {\n      this.player.muted = false;\n    });\n    __publicField(this, \"renderSourceElement\", (source, index) => {\n      if (typeof source === \"string\") {\n        return /* @__PURE__ */ import_react.default.createElement(\"source\", { key: index, src: source });\n      }\n      return /* @__PURE__ */ import_react.default.createElement(\"source\", { key: index, ...source });\n    });\n    __publicField(this, \"renderTrack\", (track, index) => {\n      return /* @__PURE__ */ import_react.default.createElement(\"track\", { key: index, ...track });\n    });\n    __publicField(this, \"ref\", (player) => {\n      if (this.player) {\n        this.prevPlayer = this.player;\n      }\n      this.player = player;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n    this.addListeners(this.player);\n    const src = this.getSource(this.props.url);\n    if (src) {\n      this.player.src = src;\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      this.player.load();\n    }\n  }\n  componentDidUpdate(prevProps) {\n    if (this.shouldUseAudio(this.props) !== this.shouldUseAudio(prevProps)) {\n      this.removeListeners(this.prevPlayer, prevProps.url);\n      this.addListeners(this.player);\n    }\n    if (this.props.url !== prevProps.url && !(0, import_utils.isMediaStream)(this.props.url) && !(this.props.url instanceof Array)) {\n      this.player.srcObject = null;\n    }\n  }\n  componentWillUnmount() {\n    this.player.removeAttribute(\"src\");\n    this.removeListeners(this.player);\n    if (this.hls) {\n      this.hls.destroy();\n    }\n  }\n  addListeners(player) {\n    const { url, playsinline } = this.props;\n    player.addEventListener(\"play\", this.onPlay);\n    player.addEventListener(\"waiting\", this.onBuffer);\n    player.addEventListener(\"playing\", this.onBufferEnd);\n    player.addEventListener(\"pause\", this.onPause);\n    player.addEventListener(\"seeked\", this.onSeek);\n    player.addEventListener(\"ended\", this.onEnded);\n    player.addEventListener(\"error\", this.onError);\n    player.addEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.addEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.addEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.addEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    if (!this.shouldUseHLS(url)) {\n      player.addEventListener(\"canplay\", this.onReady);\n    }\n    if (playsinline) {\n      player.setAttribute(\"playsinline\", \"\");\n      player.setAttribute(\"webkit-playsinline\", \"\");\n      player.setAttribute(\"x5-playsinline\", \"\");\n    }\n  }\n  removeListeners(player, url) {\n    player.removeEventListener(\"canplay\", this.onReady);\n    player.removeEventListener(\"play\", this.onPlay);\n    player.removeEventListener(\"waiting\", this.onBuffer);\n    player.removeEventListener(\"playing\", this.onBufferEnd);\n    player.removeEventListener(\"pause\", this.onPause);\n    player.removeEventListener(\"seeked\", this.onSeek);\n    player.removeEventListener(\"ended\", this.onEnded);\n    player.removeEventListener(\"error\", this.onError);\n    player.removeEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.removeEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.removeEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.removeEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    if (!this.shouldUseHLS(url)) {\n      player.removeEventListener(\"canplay\", this.onReady);\n    }\n  }\n  shouldUseAudio(props) {\n    if (props.config.forceVideo) {\n      return false;\n    }\n    if (props.config.attributes.poster) {\n      return false;\n    }\n    return import_patterns.AUDIO_EXTENSIONS.test(props.url) || props.config.forceAudio;\n  }\n  shouldUseHLS(url) {\n    if (IS_SAFARI && this.props.config.forceSafariHLS || this.props.config.forceHLS) {\n      return true;\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      return false;\n    }\n    return import_patterns.HLS_EXTENSIONS.test(url) || MATCH_CLOUDFLARE_STREAM.test(url);\n  }\n  shouldUseDASH(url) {\n    return import_patterns.DASH_EXTENSIONS.test(url) || this.props.config.forceDASH;\n  }\n  shouldUseFLV(url) {\n    return import_patterns.FLV_EXTENSIONS.test(url) || this.props.config.forceFLV;\n  }\n  load(url) {\n    const { hlsVersion, hlsOptions, dashVersion, flvVersion } = this.props.config;\n    if (this.hls) {\n      this.hls.destroy();\n    }\n    if (this.dash) {\n      this.dash.reset();\n    }\n    if (this.shouldUseHLS(url)) {\n      (0, import_utils.getSDK)(HLS_SDK_URL.replace(\"VERSION\", hlsVersion), HLS_GLOBAL).then((Hls) => {\n        this.hls = new Hls(hlsOptions);\n        this.hls.on(Hls.Events.MANIFEST_PARSED, () => {\n          this.props.onReady();\n        });\n        this.hls.on(Hls.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.hls, Hls);\n        });\n        if (MATCH_CLOUDFLARE_STREAM.test(url)) {\n          const id = url.match(MATCH_CLOUDFLARE_STREAM)[1];\n          this.hls.loadSource(REPLACE_CLOUDFLARE_STREAM.replace(\"{id}\", id));\n        } else {\n          this.hls.loadSource(url);\n        }\n        this.hls.attachMedia(this.player);\n        this.props.onLoaded();\n      });\n    }\n    if (this.shouldUseDASH(url)) {\n      (0, import_utils.getSDK)(DASH_SDK_URL.replace(\"VERSION\", dashVersion), DASH_GLOBAL).then((dashjs) => {\n        this.dash = dashjs.MediaPlayer().create();\n        this.dash.initialize(this.player, url, this.props.playing);\n        this.dash.on(\"error\", this.props.onError);\n        if (parseInt(dashVersion) < 3) {\n          this.dash.getDebug().setLogToBrowserConsole(false);\n        } else {\n          this.dash.updateSettings({ debug: { logLevel: dashjs.Debug.LOG_LEVEL_NONE } });\n        }\n        this.props.onLoaded();\n      });\n    }\n    if (this.shouldUseFLV(url)) {\n      (0, import_utils.getSDK)(FLV_SDK_URL.replace(\"VERSION\", flvVersion), FLV_GLOBAL).then((flvjs) => {\n        this.flv = flvjs.createPlayer({ type: \"flv\", url });\n        this.flv.attachMediaElement(this.player);\n        this.flv.on(flvjs.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.flv, flvjs);\n        });\n        this.flv.load();\n        this.props.onLoaded();\n      });\n    }\n    if (url instanceof Array) {\n      this.player.load();\n    } else if ((0, import_utils.isMediaStream)(url)) {\n      try {\n        this.player.srcObject = url;\n      } catch (e) {\n        this.player.src = window.URL.createObjectURL(url);\n      }\n    }\n  }\n  play() {\n    const promise = this.player.play();\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.player.pause();\n  }\n  stop() {\n    this.player.removeAttribute(\"src\");\n    if (this.dash) {\n      this.dash.reset();\n    }\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.player.currentTime = seconds;\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.player.volume = fraction;\n  }\n  enablePIP() {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture();\n    } else if ((0, import_utils.supportsWebKitPresentationMode)(this.player) && this.player.webkitPresentationMode !== \"picture-in-picture\") {\n      this.player.webkitSetPresentationMode(\"picture-in-picture\");\n    }\n  }\n  disablePIP() {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture();\n    } else if ((0, import_utils.supportsWebKitPresentationMode)(this.player) && this.player.webkitPresentationMode !== \"inline\") {\n      this.player.webkitSetPresentationMode(\"inline\");\n    }\n  }\n  setPlaybackRate(rate) {\n    try {\n      this.player.playbackRate = rate;\n    } catch (error) {\n      this.props.onError(error);\n    }\n  }\n  getDuration() {\n    if (!this.player)\n      return null;\n    const { duration, seekable } = this.player;\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1);\n    }\n    return duration;\n  }\n  getCurrentTime() {\n    if (!this.player)\n      return null;\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    if (!this.player)\n      return null;\n    const { buffered } = this.player;\n    if (buffered.length === 0) {\n      return 0;\n    }\n    const end = buffered.end(buffered.length - 1);\n    const duration = this.getDuration();\n    if (end > duration) {\n      return duration;\n    }\n    return end;\n  }\n  getSource(url) {\n    const useHLS = this.shouldUseHLS(url);\n    const useDASH = this.shouldUseDASH(url);\n    const useFLV = this.shouldUseFLV(url);\n    if (url instanceof Array || (0, import_utils.isMediaStream)(url) || useHLS || useDASH || useFLV) {\n      return void 0;\n    }\n    if (MATCH_DROPBOX_URL.test(url)) {\n      return url.replace(\"www.dropbox.com\", \"dl.dropboxusercontent.com\");\n    }\n    return url;\n  }\n  render() {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props;\n    const useAudio = this.shouldUseAudio(this.props);\n    const Element = useAudio ? \"audio\" : \"video\";\n    const style = {\n      width: width === \"auto\" ? width : \"100%\",\n      height: height === \"auto\" ? height : \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      Element,\n      {\n        ref: this.ref,\n        src: this.getSource(url),\n        style,\n        preload: \"auto\",\n        autoPlay: playing || void 0,\n        controls,\n        muted,\n        loop,\n        ...config.attributes\n      },\n      url instanceof Array && url.map(this.renderSourceElement),\n      config.tracks.map(this.renderTrack)\n    );\n  }\n}\n__publicField(FilePlayer, \"displayName\", \"FilePlayer\");\n__publicField(FilePlayer, \"canPlay\", import_patterns.canPlay.file);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "FilePlayer_exports", "__export", "target", "all", "name", "default", "FilePlayer", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "HAS_NAVIGATOR", "navigator", "IS_IPAD_PRO", "platform", "maxTouchPoints", "IS_IOS", "test", "userAgent", "window", "MSStream", "IS_SAFARI", "MATCH_DROPBOX_URL", "MATCH_CLOUDFLARE_STREAM", "Component", "constructor", "_this", "super", "arguments", "this", "props", "onReady", "onPlay", "onBuffer", "onBufferEnd", "onPause", "onEnded", "onError", "event", "onPlaybackRateChange", "playbackRate", "onEnablePIP", "e", "onDisablePIP", "playing", "play", "player", "supportsWebKitPresentationMode", "webkitPresentationMode", "onSeek", "currentTime", "muted", "source", "index", "createElement", "src", "track", "prevPlayer", "componentDidMount", "onMount", "addListeners", "getSource", "url", "config", "forceDisableHls", "load", "componentDidUpdate", "prevProps", "shouldUseAudio", "removeListeners", "isMediaStream", "Array", "srcObject", "componentWillUnmount", "removeAttribute", "hls", "destroy", "playsinline", "addEventListener", "onPlayBackRateChange", "onPresentationModeChange", "shouldUseHLS", "setAttribute", "removeEventListener", "forceVideo", "attributes", "poster", "AUDIO_EXTENSIONS", "forceAudio", "forceSafariHLS", "forceHLS", "HLS_EXTENSIONS", "shouldUseDASH", "DASH_EXTENSIONS", "forceDASH", "shouldUseFLV", "FLV_EXTENSIONS", "forceFLV", "hlsVersion", "hlsOptions", "dashVersion", "flvVersion", "dash", "reset", "getSDK", "replace", "then", "Hls", "on", "Events", "MANIFEST_PARSED", "ERROR", "data", "id", "match", "loadSource", "attachMedia", "onLoaded", "dashjs", "MediaPlayer", "initialize", "parseInt", "getDebug", "setLogToBrowserConsole", "updateSettings", "debug", "logLevel", "Debug", "LOG_LEVEL_NONE", "flvjs", "flv", "createPlayer", "type", "attachMediaElement", "URL", "createObjectURL", "promise", "catch", "pause", "stop", "seekTo", "seconds", "keepPlaying", "length", "undefined", "setVolume", "fraction", "volume", "enablePIP", "requestPictureInPicture", "document", "pictureInPictureElement", "webkitSetPresentationMode", "disablePIP", "exitPictureInPicture", "setPlaybackRate", "rate", "error", "getDuration", "duration", "seekable", "Infinity", "end", "getCurrentTime", "getSecondsLoaded", "buffered", "useHLS", "useDASH", "useFLV", "render", "loop", "controls", "width", "height", "Element", "style", "ref", "preload", "autoPlay", "map", "renderSourceElement", "tracks", "renderTrack", "canPlay", "file"], "sourceRoot": ""}