# Email Service Setup Guide

## Overview
The Allemni online platform supports multiple email services for sending emails:
- **SendGrid** (Primary service for application emails)
- **Gmail SMTP** (For password reset emails)
- **Generic SMTP** (Fallback service)

## Current Status
✅ **Email service is now ACTIVE and functional**
⚠️ **Configuration required for production use**

## Configuration Steps

### 1. SendGrid Setup (Recommended)

1. **Create SendGrid Account**
   - Go to [SendGrid.com](https://sendgrid.com)
   - Sign up for a free account (100 emails/day free tier)

2. **Generate API Key**
   - Go to Settings > API Keys
   - Click "Create API Key"
   - Choose "Restricted Access"
   - Give permissions for "Mail Send"
   - Copy the generated API key

3. **Update .env file**
   ```env
   SENDGRID_API_KEY=*********************************************************************
   EMAIL_FROM_NAME=Allemni online
   EMAIL_FROM_ADDRESS=<EMAIL>
   ```

### 2. Gmail SMTP Setup (For Password Reset)

1. **Enable 2-Factor Authentication**
   - Go to Google Account settings
   - Security > 2-Step Verification
   - Enable if not already enabled

2. **Generate App Password**
   - Go to Security > App passwords
   - Select "Mail" as the app
   - Generate password (16 characters)

3. **Update .env file**
   ```env
   EMAIL_USER=<EMAIL>
   EMAIL_PASSWORD=your-16-character-app-password
   ```

### 3. Environment Variables

Complete `.env` configuration:
```env
# SendGrid Configuration (Primary)
SENDGRID_API_KEY=*********************************************************************
EMAIL_FROM_NAME=Allemni online
EMAIL_FROM_ADDRESS=<EMAIL>

# Gmail Configuration (Password Reset)
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Email Templates (optional)
EMAIL_TEMPLATE_APPROVAL=
EMAIL_TEMPLATE_REJECTION=
```

## Testing Email Service

### 1. Using Admin Panel
1. Login as admin
2. Go to Admin Dashboard
3. Navigate to "Email Test" section
4. Check service status
5. Send test emails

### 2. Using API Endpoints

**Check Status:**
```bash
GET /api/admin/email-test/status
```

**Send Test Email:**
```bash
POST /api/admin/email-test/test
{
  "to": "<EMAIL>",
  "subject": "Test Email",
  "message": "This is a test message",
  "service": "sendgrid"
}
```

**Test Specific Service:**
```bash
POST /api/admin/email-test/test-service/sendgrid
{
  "to": "<EMAIL>"
}
```

## Email Types Supported

### 1. Teacher Application Emails
- **Approval emails** - Sent when teacher applications are approved
- **Rejection emails** - Sent when teacher applications are rejected
- **Custom templates** - Admin can customize email templates

### 2. Password Reset Emails
- **Reset code emails** - 6-digit verification codes
- **Secure delivery** - Uses Gmail SMTP for reliability

### 3. Test Emails
- **Service testing** - Test individual email services
- **Template testing** - Preview email templates

## Service Priority

The system automatically selects the best available service:

1. **SendGrid** (if configured and available)
2. **Gmail SMTP** (if configured and available)
3. **Generic SMTP** (fallback)

## Troubleshooting

### Common Issues

1. **SendGrid API Key Invalid**
   - Check if API key is correct
   - Verify API key permissions include "Mail Send"
   - Check SendGrid account status

2. **Gmail Authentication Failed**
   - Ensure 2FA is enabled
   - Generate new App Password
   - Check email/password in .env file

3. **Emails Not Sending**
   - Check server logs for error messages
   - Verify network connectivity
   - Test with email testing endpoints

### Log Messages

**Success Messages:**
- `✅ SendGrid API key configured successfully`
- `✅ Gmail SMTP server is ready to send emails`
- `✅ Email sent successfully via SENDGRID`

**Error Messages:**
- `❌ SendGrid send failed, falling back to SMTP`
- `❌ Gmail SMTP configuration error`
- `❌ Error sending email`

## Security Notes

1. **Never commit API keys** to version control
2. **Use environment variables** for all sensitive data
3. **Rotate API keys** regularly
4. **Monitor email usage** to prevent abuse
5. **Use HTTPS** for all email-related endpoints

## Production Deployment

1. **Verify all environment variables** are set correctly
2. **Test email services** before going live
3. **Monitor email delivery** rates
4. **Set up email analytics** in SendGrid dashboard
5. **Configure domain authentication** for better deliverability

## Support

If you encounter issues:
1. Check the server logs
2. Use the email testing endpoints
3. Verify environment configuration
4. Contact SendGrid support for API issues
5. Check Gmail security settings for SMTP issues
