{"version": 3, "file": "static/js/reactPlayerMixcloud.fde681cd.chunk.js", "mappings": "iFAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAmB,CAAC,EAzBTC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAkB,CACzBK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,OAC/BC,EAAeD,EAAQ,MACvBE,EAAkBF,EAAQ,MAG9B,MAAMP,UAAiBG,EAAaO,UAClCC,WAAAA,GACEC,SAASC,WACTzB,EAAc0B,KAAM,aAAcN,EAAaO,YAC/C3B,EAAc0B,KAAM,WAAY,MAChC1B,EAAc0B,KAAM,cAAe,MACnC1B,EAAc0B,KAAM,gBAAiB,MACrC1B,EAAc0B,KAAM,QAAQ,SAE5B1B,EAAc0B,KAAM,UAAU,SAE9B1B,EAAc0B,KAAM,OAAQE,IAC1BF,KAAKE,OAASA,CAAM,GAExB,CACAC,iBAAAA,GACEH,KAAKI,MAAMC,SAAWL,KAAKI,MAAMC,QAAQL,KAC3C,CACAM,IAAAA,CAAKC,IACH,EAAIb,EAAac,QArBL,oDACG,YAoB+BC,MAAMC,IAClDV,KAAKW,OAASD,EAAUE,aAAaZ,KAAKE,QAC1CF,KAAKW,OAAOE,MAAMJ,MAAK,KACrBT,KAAKW,OAAOG,OAAOC,KAAKC,GAAGhB,KAAKI,MAAMa,QACtCjB,KAAKW,OAAOG,OAAOI,MAAMF,GAAGhB,KAAKI,MAAMe,SACvCnB,KAAKW,OAAOG,OAAOM,MAAMJ,GAAGhB,KAAKI,MAAMiB,SACvCrB,KAAKW,OAAOG,OAAOQ,MAAMN,GAAGhB,KAAKI,MAAMkB,OACvCtB,KAAKW,OAAOG,OAAOS,SAASP,IAAG,CAACQ,EAASC,KACvCzB,KAAK0B,YAAcF,EACnBxB,KAAKyB,SAAWA,CAAQ,IAE1BzB,KAAKI,MAAMuB,SAAS,GACpB,GACD3B,KAAKI,MAAMwB,QAChB,CACAb,IAAAA,GACEf,KAAKC,WAAW,OAClB,CACAiB,KAAAA,GACElB,KAAKC,WAAW,QAClB,CACA4B,IAAAA,GACA,CACAC,MAAAA,CAAON,GAA6B,IAApBO,IAAWhC,UAAAiC,OAAA,QAAAC,IAAAlC,UAAA,KAAAA,UAAA,GACzBC,KAAKC,WAAW,OAAQuB,GACnBO,GACH/B,KAAKkB,OAET,CACAgB,SAAAA,CAAUC,GACV,CACAC,WAAAA,GACE,OAAOpC,KAAKyB,QACd,CACAY,cAAAA,GACE,OAAOrC,KAAK0B,WACd,CACAY,gBAAAA,GACE,OAAO,IACT,CACAC,MAAAA,GACE,MAAM,IAAEhC,EAAG,OAAEiC,GAAWxC,KAAKI,MACvBqC,EAAKlC,EAAImC,MAAM/C,EAAgBgD,oBAAoB,GAKnDC,GAAQ,EAAIlD,EAAamD,aAAa,IACvCL,EAAOM,QACVC,KAAM,IAAIN,OAEZ,OAAuBpD,EAAaJ,QAAQ+D,cAC1C,SACA,CACE9E,IAAKuE,EACLQ,IAAKjD,KAAKiD,IACVC,MAbU,CACZC,MAAO,OACPC,OAAQ,QAYNC,IAAK,2CAA2CT,IAChDU,YAAa,IACbC,MAAO,YAGb,EAEFjF,EAAcY,EAAU,cAAe,YACvCZ,EAAcY,EAAU,UAAWS,EAAgB6D,QAAQC,UAC3DnF,EAAcY,EAAU,eAAe,E", "sources": ["../node_modules/react-player/lib/players/Mixcloud.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Mixcloud_exports = {};\n__export(Mixcloud_exports, {\n  default: () => Mixcloud\n});\nmodule.exports = __toCommonJS(Mixcloud_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://widget.mixcloud.com/media/js/widgetApi.js\";\nconst SDK_GLOBAL = \"Mixcloud\";\nclass Mixcloud extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n    });\n    __publicField(this, \"unmute\", () => {\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Mixcloud2) => {\n      this.player = Mixcloud2.PlayerWidget(this.iframe);\n      this.player.ready.then(() => {\n        this.player.events.play.on(this.props.onPlay);\n        this.player.events.pause.on(this.props.onPause);\n        this.player.events.ended.on(this.props.onEnded);\n        this.player.events.error.on(this.props.error);\n        this.player.events.progress.on((seconds, duration) => {\n          this.currentTime = seconds;\n          this.duration = duration;\n        });\n        this.props.onReady();\n      });\n    }, this.props.onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { url, config } = this.props;\n    const id = url.match(import_patterns.MATCH_URL_MIXCLOUD)[1];\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    const query = (0, import_utils.queryString)({\n      ...config.options,\n      feed: `/${id}/`\n    });\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        key: id,\n        ref: this.ref,\n        style,\n        src: `https://www.mixcloud.com/widget/iframe/?${query}`,\n        frameBorder: \"0\",\n        allow: \"autoplay\"\n      }\n    );\n  }\n}\n__publicField(Mixcloud, \"displayName\", \"Mixcloud\");\n__publicField(Mixcloud, \"canPlay\", import_patterns.canPlay.mixcloud);\n__publicField(Mixcloud, \"loopOnEnded\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "Mixcloud_exports", "__export", "target", "all", "name", "default", "Mixcloud", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "iframe", "componentDidMount", "props", "onMount", "load", "url", "getSDK", "then", "Mixcloud2", "player", "PlayerWidget", "ready", "events", "play", "on", "onPlay", "pause", "onPause", "ended", "onEnded", "error", "progress", "seconds", "duration", "currentTime", "onReady", "onError", "stop", "seekTo", "keepPlaying", "length", "undefined", "setVolume", "fraction", "getDuration", "getCurrentTime", "getSecondsLoaded", "render", "config", "id", "match", "MATCH_URL_MIXCLOUD", "query", "queryString", "options", "feed", "createElement", "ref", "style", "width", "height", "src", "frameBorder", "allow", "canPlay", "mixcloud"], "sourceRoot": ""}