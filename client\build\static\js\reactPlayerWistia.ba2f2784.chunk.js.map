{"version": 3, "file": "static/js/reactPlayerWistia.ba2f2784.chunk.js", "mappings": "kFAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAiB,CAAC,EAzBPC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAgB,CACvBK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,OAC/BC,EAAeD,EAAQ,MACvBE,EAAkBF,EAAQ,MAI9B,MAAMP,UAAeG,EAAaO,UAChCC,WAAAA,GAAc,IAAAC,EACZC,SAASC,WAAUF,EAAAG,KACnB3B,EAAc2B,KAAM,aAAcP,EAAaQ,YAC/C5B,EAAc2B,KAAM,WAAYA,KAAKE,MAAMC,OAAOC,UAAY,kBAAsB,EAAIX,EAAaY,mBAErGhC,EAAc2B,KAAM,UAAU,kBAAaH,EAAKK,MAAMI,UAAOP,UAAQ,IACrE1B,EAAc2B,KAAM,WAAW,kBAAaH,EAAKK,MAAMK,WAAQR,UAAQ,IACvE1B,EAAc2B,KAAM,UAAU,kBAAaH,EAAKK,MAAMM,UAAOT,UAAQ,IACrE1B,EAAc2B,KAAM,WAAW,kBAAaH,EAAKK,MAAMO,WAAQV,UAAQ,IACvE1B,EAAc2B,KAAM,wBAAwB,kBAAaH,EAAKK,MAAMQ,wBAAqBX,UAAQ,IACjG1B,EAAc2B,KAAM,QAAQ,KAC1BA,KAAKC,WAAW,OAAO,IAEzB5B,EAAc2B,KAAM,UAAU,KAC5BA,KAAKC,WAAW,SAAS,GAE7B,CACAU,iBAAAA,GACEX,KAAKE,MAAMU,SAAWZ,KAAKE,MAAMU,QAAQZ,KAC3C,CACAa,IAAAA,CAAKC,GACH,MAAM,QAAEC,EAAO,MAAEC,EAAK,SAAEC,EAAQ,QAAEC,EAAO,OAAEf,EAAM,QAAEgB,GAAYnB,KAAKE,OACpE,EAAIT,EAAa2B,QA1BL,kDACG,UAyB+BC,MAAMC,IAC9CnB,EAAOoB,gBACTpB,EAAOoB,eAAeC,SAASC,GAAYH,EAAQI,cAAcD,KAEnEE,OAAOC,IAAMD,OAAOC,KAAO,GAC3BD,OAAOC,IAAIC,KAAK,CACdC,GAAI9B,KAAK+B,SACTC,QAAS,CACPC,SAAUlB,EACVmB,eAAgB,QAChBlB,QACAmB,sBAAuBlB,EACvBmB,iBAAkBnB,EAClBoB,QAASpB,EACTqB,oBAAqBrB,EACrBsB,eAAgBtB,EAChBuB,cAAevB,EACfwB,gBAAiBxB,EACjByB,gBAAiBzB,KACdd,EAAO6B,SAEZd,QAAUyB,IACR3C,KAAK2C,OAASA,EACd3C,KAAK4C,SACL5C,KAAK2C,OAAOE,KAAK,OAAQ7C,KAAKM,QAC9BN,KAAK2C,OAAOE,KAAK,QAAS7C,KAAKO,SAC/BP,KAAK2C,OAAOE,KAAK,OAAQ7C,KAAKQ,QAC9BR,KAAK2C,OAAOE,KAAK,MAAO7C,KAAKS,SAC7BT,KAAK2C,OAAOE,KAAK,qBAAsB7C,KAAKU,sBAC5CQ,GAAS,GAEX,GACDC,EACL,CACAyB,MAAAA,GACE5C,KAAK2C,OAAOC,OAAO,OAAQ5C,KAAKM,QAChCN,KAAK2C,OAAOC,OAAO,QAAS5C,KAAKO,SACjCP,KAAK2C,OAAOC,OAAO,OAAQ5C,KAAKQ,QAChCR,KAAK2C,OAAOC,OAAO,MAAO5C,KAAKS,SAC/BT,KAAK2C,OAAOC,OAAO,qBAAsB5C,KAAKU,qBAChD,CACAoC,IAAAA,GACE9C,KAAKC,WAAW,OAClB,CACA8C,KAAAA,GACE/C,KAAKC,WAAW,QAClB,CACA+C,IAAAA,GACEhD,KAAK4C,SACL5C,KAAKC,WAAW,SAClB,CACAgD,MAAAA,CAAOC,GAA6B,IAApBC,IAAWpD,UAAAqD,OAAA,QAAAC,IAAAtD,UAAA,KAAAA,UAAA,GACzBC,KAAKC,WAAW,OAAQiD,GACnBC,GACHnD,KAAK+C,OAET,CACAO,SAAAA,CAAUC,GACRvD,KAAKC,WAAW,SAAUsD,EAC5B,CACAC,eAAAA,CAAgBC,GACdzD,KAAKC,WAAW,eAAgBwD,EAClC,CACAC,WAAAA,GACE,OAAO1D,KAAKC,WAAW,WACzB,CACA0D,cAAAA,GACE,OAAO3D,KAAKC,WAAW,OACzB,CACA2D,gBAAAA,GACE,OAAO,IACT,CACAC,MAAAA,GACE,MAAM,IAAE/C,GAAQd,KAAKE,MACf4D,EAAUhD,GAAOA,EAAIiD,MAAMrE,EAAgBsE,kBAAkB,GAC7DC,EAAY,6BAA6BH,IAK/C,OAAuB1E,EAAaJ,QAAQkF,cAAc,MAAO,CAAEpC,GAAI9B,KAAK+B,SAAU9D,IAAK6F,EAASG,YAAWE,MAJjG,CACZC,MAAO,OACPC,OAAQ,SAGZ,EAEFhG,EAAcY,EAAQ,cAAe,UACrCZ,EAAcY,EAAQ,UAAWS,EAAgB4E,QAAQC,QACzDlG,EAAcY,EAAQ,eAAe,E", "sources": ["../node_modules/react-player/lib/players/Wistia.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Wistia_exports = {};\n__export(Wistia_exports, {\n  default: () => Wistia\n});\nmodule.exports = __toCommonJS(Wistia_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://fast.wistia.com/assets/external/E-v1.js\";\nconst SDK_GLOBAL = \"Wistia\";\nconst PLAYER_ID_PREFIX = \"wistia-player-\";\nclass Wistia extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onSeek\", (...args) => this.props.onSeek(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onPlaybackRateChange\", (...args) => this.props.onPlaybackRateChange(...args));\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { playing, muted, controls, onReady, config, onError } = this.props;\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Wistia2) => {\n      if (config.customControls) {\n        config.customControls.forEach((control) => Wistia2.defineControl(control));\n      }\n      window._wq = window._wq || [];\n      window._wq.push({\n        id: this.playerID,\n        options: {\n          autoPlay: playing,\n          silentAutoPlay: \"allow\",\n          muted,\n          controlsVisibleOnLoad: controls,\n          fullscreenButton: controls,\n          playbar: controls,\n          playbackRateControl: controls,\n          qualityControl: controls,\n          volumeControl: controls,\n          settingsControl: controls,\n          smallPlayButton: controls,\n          ...config.options\n        },\n        onReady: (player) => {\n          this.player = player;\n          this.unbind();\n          this.player.bind(\"play\", this.onPlay);\n          this.player.bind(\"pause\", this.onPause);\n          this.player.bind(\"seek\", this.onSeek);\n          this.player.bind(\"end\", this.onEnded);\n          this.player.bind(\"playbackratechange\", this.onPlaybackRateChange);\n          onReady();\n        }\n      });\n    }, onError);\n  }\n  unbind() {\n    this.player.unbind(\"play\", this.onPlay);\n    this.player.unbind(\"pause\", this.onPause);\n    this.player.unbind(\"seek\", this.onSeek);\n    this.player.unbind(\"end\", this.onEnded);\n    this.player.unbind(\"playbackratechange\", this.onPlaybackRateChange);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.unbind();\n    this.callPlayer(\"remove\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"time\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"volume\", fraction);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"playbackRate\", rate);\n  }\n  getDuration() {\n    return this.callPlayer(\"duration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"time\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { url } = this.props;\n    const videoID = url && url.match(import_patterns.MATCH_URL_WISTIA)[1];\n    const className = `wistia_embed wistia_async_${videoID}`;\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { id: this.playerID, key: videoID, className, style });\n  }\n}\n__publicField(Wistia, \"displayName\", \"Wistia\");\n__publicField(Wistia, \"canPlay\", import_patterns.canPlay.wistia);\n__publicField(Wistia, \"loopOnEnded\", true);\n"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "Wistia_exports", "__export", "target", "all", "name", "default", "Wistia", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "Component", "constructor", "_this", "super", "arguments", "this", "callPlayer", "props", "config", "playerId", "randomString", "onPlay", "onPause", "onSeek", "onEnded", "onPlaybackRateChange", "componentDidMount", "onMount", "load", "url", "playing", "muted", "controls", "onReady", "onError", "getSDK", "then", "Wistia2", "customControls", "for<PERSON>ach", "control", "defineControl", "window", "_wq", "push", "id", "playerID", "options", "autoPlay", "silentAutoPlay", "controlsVisibleOnLoad", "fullscreenButton", "playbar", "playbackRateControl", "qualityControl", "volumeControl", "settingsControl", "smallPlayButton", "player", "unbind", "bind", "play", "pause", "stop", "seekTo", "seconds", "keepPlaying", "length", "undefined", "setVolume", "fraction", "setPlaybackRate", "rate", "getDuration", "getCurrentTime", "getSecondsLoaded", "render", "videoID", "match", "MATCH_URL_WISTIA", "className", "createElement", "style", "width", "height", "canPlay", "wistia"], "sourceRoot": ""}