{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useNavigate,useLocation}from'react-router-dom';import axios from'../utils/axios';import{Box,CssBaseline,Drawer,AppBar,Toolbar,List,Typography,Divider,IconButton,ListItem,ListItemIcon,ListItemText,ListItemButton,Container,useTheme,useMediaQuery,Tooltip,Menu,MenuItem,Avatar,Button,alpha,Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,Badge,Link}from'@mui/material';import{Menu as MenuIcon,Dashboard as DashboardIcon,Person as PersonIcon,School as SchoolIcon,Chat as ChatIcon,Search as SearchIcon,Category as CategoryIcon,Assignment as AssignmentIcon,Translate as TranslateIcon,KeyboardArrowDown as KeyboardArrowDownIcon,Language as LanguageIcon,Logout as LogoutIcon,AccountCircle as AccountCircleIcon,VideoCall as VideoCallIcon,ContactSupport as ContactSupportIcon,AccountBalanceWallet as AccountBalanceWalletIcon,Payment as PaymentIcon,TrendingUp as TrendingUpIcon,Email as EmailIcon,CalendarMonth as CalendarMonthIcon,Star as StarIcon,RateReview as RateReviewIcon,Edit as EditIcon,AccessTime as AccessTimeIcon,Policy as PolicyIcon,MenuBook as MenuBookIcon,ReportProblem as ReportProblemIcon}from'@mui/icons-material';import dayjs from'dayjs';import utc from'dayjs/plugin/utc';import{useAuth}from'../contexts/AuthContext';import MeetingFeedbackDialog from'./MeetingFeedbackDialog';import{useUnreadMessages}from'../contexts/UnreadMessagesContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";dayjs.extend(utc);const drawerWidth=240;const Layout=_ref=>{var _currentUser$full_nam;let{children}=_ref;const[pendingDialogOpen,setPendingDialogOpen]=useState(false);const[pendingIssue,setPendingIssue]=useState(null);const{t,i18n}=useTranslation();const{currentUser,handleLogout}=useAuth();const navigate=useNavigate();const location=useLocation();const theme=useTheme();const isRtl=i18n.language==='ar';const isMobile=useMediaQuery(theme.breakpoints.down('sm'));const isTablet=useMediaQuery(theme.breakpoints.between('sm','md'));const[mobileOpen,setMobileOpen]=useState(false);const[anchorEl,setAnchorEl]=useState(null);const[langMenuAnchor,setLangMenuAnchor]=useState(null);const[logoutDialogOpen,setLogoutDialogOpen]=useState(false);const[balance,setBalance]=useState(null);const{unreadCount}=useUnreadMessages();// Helper to fetch pending feedback\nconst fetchPending=async()=>{if((currentUser===null||currentUser===void 0?void 0:currentUser.role)!=='student')return;try{const{data}=await axios.get('/meeting-issues/pending');if(data.success&&data.data){setPendingIssue(data.data);setPendingDialogOpen(true);}}catch(err){console.error('Error fetching pending feedback',err);}};// Fetch on mount / route change\nuseEffect(()=>{fetchPending();},[currentUser,location.pathname]);useEffect(()=>{document.dir=isRtl?'rtl':'ltr';},[isRtl]);useEffect(()=>{if(currentUser){fetchBalance();}},[currentUser]);const fetchBalance=async()=>{try{const response=await axios.get('/api/wallet/balance');if(response.data.success){setBalance(response.data.balance);}}catch(error){console.error('Error fetching balance:',error);}};const handleDrawerToggle=()=>{const newState=!mobileOpen;setMobileOpen(newState);if(isMobile){localStorage.setItem('drawerOpen',newState?'true':'false');}// Check for pending feedback when drawer opens\nif(newState){fetchPending();}};const handleLangMenuOpen=event=>{setLangMenuAnchor(event.currentTarget);};const handleLangMenuClose=()=>{setLangMenuAnchor(null);};const handleLanguageChange=lang=>{i18n.changeLanguage(lang);localStorage.setItem('language',lang);handleLangMenuClose();};const getMenuItems=()=>{if((currentUser===null||currentUser===void 0?void 0:currentUser.role)==='platform_teacher'){return[{text:t('menu.dashboard'),icon:/*#__PURE__*/_jsx(DashboardIcon,{}),path:'/teacher/dashboard'},{text:t('bookings.title'),icon:/*#__PURE__*/_jsx(CalendarMonthIcon,{}),path:'/teacher/bookings'},{text:t('menu.meetings'),icon:/*#__PURE__*/_jsx(VideoCallIcon,{}),path:'/teacher/meetings'},{text:t('teacher.myLessons'),icon:/*#__PURE__*/_jsx(MenuBookIcon,{}),path:'/teacher/my-lessons'},{text:t('menu.profile'),icon:/*#__PURE__*/_jsx(PersonIcon,{}),path:'/teacher/profile'},{text:t('menu.chat'),icon:/*#__PURE__*/_jsx(ChatIcon,{}),path:'/teacher/chat',badge:unreadCount},{text:t('reviews.teacherReviews'),icon:/*#__PURE__*/_jsx(StarIcon,{}),path:'/teacher/reviews'},{text:t('wallet.title'),icon:/*#__PURE__*/_jsx(AccountBalanceWalletIcon,{}),path:'/teacher/wallet'},{text:t('withdrawal.title'),icon:/*#__PURE__*/_jsx(PaymentIcon,{}),path:'/teacher/withdrawal'},{text:t('contactUs.title'),icon:/*#__PURE__*/_jsx(ContactSupportIcon,{}),path:'/teacher/contact-us'},{text:t('myMessages.title'),icon:/*#__PURE__*/_jsx(EmailIcon,{}),path:'/teacher/my-messages'},{text:t('menu.platformPolicy'),icon:/*#__PURE__*/_jsx(PolicyIcon,{}),path:'/platform-policy'}];}const adminItems=[{text:t('nav.dashboard'),path:'/admin/dashboard',icon:/*#__PURE__*/_jsx(DashboardIcon,{})},{text:t('nav.applications'),path:'/admin/applications',icon:/*#__PURE__*/_jsx(AssignmentIcon,{})},{text:t('nav.teachers'),path:'/admin/teachers',icon:/*#__PURE__*/_jsx(SchoolIcon,{})},{text:t('nav.students'),path:'/admin/students',icon:/*#__PURE__*/_jsx(PersonIcon,{})},{text:t('nav.profileUpdates'),path:'/admin/profile-updates',icon:/*#__PURE__*/_jsx(EditIcon,{})},{text:t('admin.meetingSessions.title'),path:'/admin/meeting-sessions',icon:/*#__PURE__*/_jsx(AccessTimeIcon,{})},{text:t('admin.meetingIssues.title','Meeting Issues'),path:'/admin/meeting-issues',icon:/*#__PURE__*/_jsx(ReportProblemIcon,{})},{text:t('nav.categories'),path:'/admin/categories',icon:/*#__PURE__*/_jsx(CategoryIcon,{})},{text:t('nav.languages'),path:'/admin/languages',icon:/*#__PURE__*/_jsx(LanguageIcon,{})},{text:t('nav.withdrawalManagement'),icon:/*#__PURE__*/_jsx(PaymentIcon,{}),path:'/admin/withdrawals'},{text:t('admin.earnings.title'),icon:/*#__PURE__*/_jsx(TrendingUpIcon,{}),path:'/admin/earnings'},{text:t('common.profile'),path:'/admin/profile',icon:/*#__PURE__*/_jsx(PersonIcon,{})},{text:t('wallet.title'),path:'/admin/wallet',icon:/*#__PURE__*/_jsx(AccountBalanceWalletIcon,{})},{text:t('admin.messages.title'),path:'/admin/messages',icon:/*#__PURE__*/_jsx(ContactSupportIcon,{})},{text:t('menu.platformPolicy'),path:'/platform-policy',icon:/*#__PURE__*/_jsx(PolicyIcon,{})}];const newTeacherItems=[{text:t('nav.applications'),path:'/teacher/application',icon:/*#__PURE__*/_jsx(AssignmentIcon,{})},{text:t('common.profile'),path:'/teacher/profile',icon:/*#__PURE__*/_jsx(PersonIcon,{})},{text:t('menu.platformPolicy'),path:'/platform-policy',icon:/*#__PURE__*/_jsx(PolicyIcon,{})}];const studentItems=[{text:t('nav.dashboard'),path:'/student/dashboard',icon:/*#__PURE__*/_jsx(DashboardIcon,{})},{text:t('nav.findTeacher'),path:'/student/find-teacher',icon:/*#__PURE__*/_jsx(SearchIcon,{})},{text:t('bookings.title'),path:'/student/bookings',icon:/*#__PURE__*/_jsx(CalendarMonthIcon,{})},{text:t('nav.myTeachers'),path:'/student/my-teachers',icon:/*#__PURE__*/_jsx(SchoolIcon,{})},{text:t('nav.meetings'),path:'/student/meetings',icon:/*#__PURE__*/_jsx(VideoCallIcon,{})},{text:t('nav.chat'),path:'/student/chat',icon:/*#__PURE__*/_jsx(ChatIcon,{}),badge:unreadCount},{text:t('reviews.writeReview'),path:'/student/write-review',icon:/*#__PURE__*/_jsx(RateReviewIcon,{})},{text:t('common.profile'),path:'/student/profile',icon:/*#__PURE__*/_jsx(PersonIcon,{})},{text:t('wallet.title'),path:'/student/wallet',icon:/*#__PURE__*/_jsx(AccountBalanceWalletIcon,{})},{text:t('contactUs.title'),path:'/student/contact-us',icon:/*#__PURE__*/_jsx(ContactSupportIcon,{})},{text:t('myMessages.title'),path:'/student/my-messages',icon:/*#__PURE__*/_jsx(EmailIcon,{})},{text:t('menu.platformPolicy'),path:'/platform-policy',icon:/*#__PURE__*/_jsx(PolicyIcon,{})}];switch(currentUser===null||currentUser===void 0?void 0:currentUser.role){case'admin':return adminItems;case'new_teacher':return newTeacherItems;case'student':return studentItems;default:return[];}};const handleProfileClick=()=>{switch(currentUser===null||currentUser===void 0?void 0:currentUser.role){case'admin':navigate('/admin/profile');break;case'platform_teacher':case'new_teacher':navigate('/teacher/profile');break;case'student':navigate('/student/profile');break;default:navigate('/login');}};const handleLogoutClick=()=>{setAnchorEl(null);// Close menu immediately for better UX\nsetLogoutDialogOpen(true);};const handleConfirmLogout=async()=>{setLogoutDialogOpen(false);try{await handleLogout();}catch(error){console.error('Logout failed:',error);}};const handleCancelLogout=()=>{setLogoutDialogOpen(false);};const drawer=/*#__PURE__*/_jsxs(Box,{children:[currentUser&&balance!==null?/*#__PURE__*/_jsxs(Box,{sx:{mx:2,my:2,p:2,bgcolor:'primary.light',borderRadius:2,display:'flex',alignItems:'center',justifyContent:'center',gap:1.5,minHeight:64},children:[/*#__PURE__*/_jsx(AccountBalanceWalletIcon,{sx:{color:'primary.contrastText',fontSize:'1.5rem'}}),/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:'primary.contrastText',fontSize:'0.75rem',opacity:0.9,display:'block',lineHeight:1.2},children:t('wallet.balance')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{color:'primary.contrastText',fontSize:'1.1rem',fontWeight:700,lineHeight:1.2,mt:0.5},children:[\"$\",balance]})]})]}):/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'center',p:2,minHeight:64},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontFamily:'Tajawal, sans-serif',fontWeight:600,color:'primary.main'},children:t('brand.name')})}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(List,{children:getMenuItems().map(item=>/*#__PURE__*/_jsxs(ListItem,{component:\"div\",onClick:()=>{if(location.pathname===item.path){navigate(0);// Reload current page\n}else{navigate(item.path);}},selected:location.pathname===item.path,sx:{minHeight:48,px:2.5,cursor:'pointer',borderRadius:1,mx:1,mb:0.5,'&:hover':{backgroundColor:'action.hover'},'&.Mui-selected':{backgroundColor:'primary.main',color:'primary.contrastText','&:hover':{backgroundColor:'primary.dark'},'& .MuiListItemIcon-root':{color:'primary.contrastText'}}},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:0,mr:isRtl?0:2,ml:isRtl?2:0,justifyContent:'center'},children:item.badge&&item.badge>0?/*#__PURE__*/_jsx(Badge,{badgeContent:item.badge,color:\"error\",max:99,children:item.icon}):item.icon}),/*#__PURE__*/_jsx(ListItemText,{primary:item.text,sx:{'& .MuiListItemText-primary':{fontSize:'0.9rem',fontWeight:500}}})]},item.text))})]});const handleGlobalFeedbackSubmit=async(meetingId,values)=>{try{await axios.post('/meeting-issues',{meeting_id:meetingId,issue_type:values.issue_type,description:values.description});}catch(err){console.error('Failed to submit feedback',err);}finally{setPendingDialogOpen(false);setPendingIssue(null);// Check if there is another pending meeting\nawait fetchPending();}};return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',direction:isRtl?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(AppBar,{position:\"fixed\",sx:{width:'100%',zIndex:theme.zIndex.drawer+1},children:/*#__PURE__*/_jsxs(Toolbar,{sx:{minHeight:{xs:56,sm:64,md:70},px:{xs:1,sm:2,md:3}},children:[/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",\"aria-label\":\"open drawer\",edge:isRtl?'end':'start',onClick:handleDrawerToggle,sx:{mr:isRtl?0:{xs:1,sm:2},ml:isRtl?{xs:1,sm:2}:0,display:{sm:'none'},p:{xs:1,sm:1.5}},children:/*#__PURE__*/_jsx(MenuIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",noWrap:true,component:\"div\",sx:{flexGrow:1,fontSize:{xs:'1rem',sm:'1.1rem',md:'1.25rem'},fontFamily:'Tajawal, sans-serif',fontWeight:{xs:500,md:600}},children:t('brand.name')}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:{xs:0.5,sm:1,md:1.5}},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Button,{color:\"inherit\",startIcon:/*#__PURE__*/_jsx(TranslateIcon,{sx:{fontSize:{xs:'1rem',sm:'1.1rem',md:'1.2rem'}}}),endIcon:/*#__PURE__*/_jsx(KeyboardArrowDownIcon,{sx:{fontSize:{xs:'1rem',sm:'1.1rem',md:'1.2rem'}}}),onClick:handleLangMenuOpen,sx:{minWidth:{xs:80,sm:100,md:120},borderRadius:2,px:{xs:1,sm:1.5,md:2},py:{xs:0.5,sm:0.75,md:1},gap:{xs:0.5,sm:0.75,md:1},fontSize:{xs:'0.75rem',sm:'0.85rem',md:'0.9rem'},'& .MuiButton-startIcon':{margin:0},'& .MuiButton-endIcon':{margin:0,ml:0.5},'&:hover':{backgroundColor:alpha(theme.palette.common.white,0.1),transform:'translateY(-1px)'},transition:'all 0.2s ease-in-out'},children:i18n.language==='ar'?'العربية':'English'}),/*#__PURE__*/_jsxs(Menu,{anchorEl:langMenuAnchor,open:Boolean(langMenuAnchor),onClose:handleLangMenuClose,anchorOrigin:{vertical:'bottom',horizontal:'right'},transformOrigin:{vertical:'top',horizontal:'right'},slotProps:{paper:{elevation:0,sx:{mt:1.5,overflow:'visible',filter:'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))','&:before':{content:'\"\"',display:'block',position:'absolute',top:0,right:14,width:10,height:10,bgcolor:'background.paper',transform:'translateY(-50%) rotate(45deg)',zIndex:0}}}},children:[/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleLanguageChange('ar'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(LanguageIcon,{fontSize:\"small\"})}),\"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"]}),/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleLanguageChange('en'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(LanguageIcon,{fontSize:\"small\"})}),\"English\"]})]})]}),currentUser&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(IconButton,{onClick:event=>setAnchorEl(event.currentTarget),sx:{ml:{xs:0.5,sm:1,md:1.5},p:{xs:0.5,sm:0.75,md:1},'&:hover':{transform:'scale(1.05)',bgcolor:'rgba(255,255,255,0.1)'},transition:'all 0.2s ease-in-out'},children:/*#__PURE__*/_jsx(Avatar,{src:currentUser.profile_picture_url?currentUser.profile_picture_url.startsWith('http')?currentUser.profile_picture_url:`https://allemnionline.com${currentUser.profile_picture_url}`:'',alt:currentUser.full_name,sx:{width:{xs:32,sm:36,md:40},height:{xs:32,sm:36,md:40},bgcolor:'primary.main',border:'2px solid',borderColor:'background.paper',fontSize:{xs:'0.9rem',sm:'1rem',md:'1.1rem'},fontWeight:600},children:!currentUser.profile_picture_url&&((_currentUser$full_nam=currentUser.full_name)===null||_currentUser$full_nam===void 0?void 0:_currentUser$full_nam.charAt(0))})}),/*#__PURE__*/_jsxs(Menu,{anchorEl:anchorEl,open:Boolean(anchorEl),onClose:()=>setAnchorEl(null),onClick:()=>setAnchorEl(null),transformOrigin:{horizontal:isRtl?'left':'right',vertical:'top'},anchorOrigin:{horizontal:isRtl?'left':'right',vertical:'bottom'},children:[/*#__PURE__*/_jsxs(MenuItem,{onClick:handleProfileClick,children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(PersonIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('common.profile')})]}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(MenuItem,{onClick:handleLogoutClick,children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(LogoutIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('auth.logout')})]})]})]}),!currentUser&&/*#__PURE__*/_jsx(Button,{color:\"inherit\",onClick:()=>navigate('/login'),sx:{ml:{xs:0.5,sm:1,md:1.5},px:{xs:1.5,sm:2,md:2.5},py:{xs:0.5,sm:0.75,md:1},fontSize:{xs:'0.8rem',sm:'0.9rem',md:'1rem'},borderRadius:2,'&:hover':{backgroundColor:alpha(theme.palette.common.white,0.1),transform:'translateY(-1px)'},transition:'all 0.2s ease-in-out'},children:t('auth.login')})]})]})}),/*#__PURE__*/_jsx(Box,{component:\"nav\",sx:{width:{sm:drawerWidth},flexShrink:{sm:0}},children:/*#__PURE__*/_jsx(Drawer,{variant:isMobile?'temporary':'permanent',open:isMobile?mobileOpen:true,onClose:handleDrawerToggle,anchor:isRtl?'right':'left',ModalProps:{keepMounted:true},sx:{'& .MuiDrawer-paper':{boxSizing:'border-box',width:drawerWidth,top:{xs:'56px',sm:'64px',md:'70px'},height:{xs:'calc(100vh - 56px)',sm:'calc(100vh - 64px)',md:'calc(100vh - 70px)'},borderRight:isRtl?'none':'1px solid rgba(0, 0, 0, 0.12)',borderLeft:isRtl?'1px solid rgba(0, 0, 0, 0.12)':'none',overflowX:'hidden',position:'fixed',zIndex:1000,'&::-webkit-scrollbar':{width:'6px'},'&::-webkit-scrollbar-track':{background:'#f1f1f1'},'&::-webkit-scrollbar-thumb':{background:'#888',borderRadius:'3px'},'&::-webkit-scrollbar-thumb:hover':{background:'#555'}}},children:drawer})}),/*#__PURE__*/_jsx(Box,{component:\"main\",sx:{flexGrow:1,p:{xs:0.5,sm:1,md:1.5},width:'100%',marginTop:{xs:'56px',sm:'64px',md:'70px'},minHeight:`calc(100vh - ${isMobile?'56px':isTablet?'64px':'70px'})`,overflow:'auto'},children:/*#__PURE__*/_jsxs(Container,{maxWidth:\"xl\",sx:{px:{xs:1,sm:2,md:3},py:{xs:1,sm:1.5,md:2}},children:[children,(currentUser===null||currentUser===void 0?void 0:currentUser.role)==='student'&&/*#__PURE__*/_jsx(MeetingFeedbackDialog,{open:pendingDialogOpen&&pendingIssue&&dayjs.utc().isAfter(dayjs.utc(pendingIssue.datetime).add(pendingIssue.duration||50,'minute')),meeting:pendingIssue,timezone:(currentUser===null||currentUser===void 0?void 0:currentUser.timezone)||null,onSubmit:handleGlobalFeedbackSubmit,onClose:null/* Prevent manual close */})]})}),/*#__PURE__*/_jsxs(Dialog,{open:logoutDialogOpen,onClose:handleCancelLogout,\"aria-labelledby\":\"logout-dialog-title\",\"aria-describedby\":\"logout-dialog-description\",children:[/*#__PURE__*/_jsx(DialogTitle,{id:\"logout-dialog-title\",children:t('auth.logoutConfirmTitle')}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(DialogContentText,{id:\"logout-dialog-description\",children:t('auth.logoutConfirmMessage')})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCancelLogout,color:\"primary\",children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{onClick:handleConfirmLogout,color:\"primary\",autoFocus:true,children:t('common.confirm')})]})]})]});};export default Layout;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "useLocation", "axios", "Box", "CssBaseline", "Drawer", "AppBar", "<PERSON><PERSON><PERSON>", "List", "Typography", "Divider", "IconButton", "ListItem", "ListItemIcon", "ListItemText", "ListItemButton", "Container", "useTheme", "useMediaQuery", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Avatar", "<PERSON><PERSON>", "alpha", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "Badge", "Link", "MenuIcon", "Dashboard", "DashboardIcon", "Person", "PersonIcon", "School", "SchoolIcon", "Cha<PERSON>", "ChatIcon", "Search", "SearchIcon", "Category", "CategoryIcon", "Assignment", "AssignmentIcon", "Translate", "TranslateIcon", "KeyboardArrowDown", "KeyboardArrowDownIcon", "Language", "LanguageIcon", "Logout", "LogoutIcon", "AccountCircle", "AccountCircleIcon", "VideoCall", "VideoCallIcon", "ContactSupport", "ContactSupportIcon", "AccountBalanceWallet", "AccountBalanceWalletIcon", "Payment", "PaymentIcon", "TrendingUp", "TrendingUpIcon", "Email", "EmailIcon", "CalendarMonth", "CalendarMonthIcon", "Star", "StarIcon", "RateReview", "RateReviewIcon", "Edit", "EditIcon", "AccessTime", "AccessTimeIcon", "Policy", "PolicyIcon", "MenuBook", "MenuBookIcon", "ReportProblem", "ReportProblemIcon", "dayjs", "utc", "useAuth", "MeetingFeedbackDialog", "useUnreadMessages", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "extend", "drawerWidth", "Layout", "_ref", "_currentUser$full_nam", "children", "pendingDialogOpen", "setPendingDialogOpen", "pendingIssue", "setPendingIssue", "t", "i18n", "currentUser", "handleLogout", "navigate", "location", "theme", "isRtl", "language", "isMobile", "breakpoints", "down", "isTablet", "between", "mobileOpen", "setMobileOpen", "anchorEl", "setAnchorEl", "langMenuAnchor", "setLangMenuAnchor", "logoutDialogOpen", "setLogoutDialogOpen", "balance", "setBalance", "unreadCount", "fetchPending", "role", "data", "get", "success", "err", "console", "error", "pathname", "document", "dir", "fetchBalance", "response", "handleDrawerToggle", "newState", "localStorage", "setItem", "handleLangMenuOpen", "event", "currentTarget", "handleLangMenuClose", "handleLanguageChange", "lang", "changeLanguage", "getMenuItems", "text", "icon", "path", "badge", "adminItems", "newTeacherItems", "studentItems", "handleProfileClick", "handleLogoutClick", "handleConfirmLogout", "handleCancelLogout", "drawer", "sx", "mx", "my", "p", "bgcolor", "borderRadius", "display", "alignItems", "justifyContent", "gap", "minHeight", "color", "fontSize", "textAlign", "variant", "opacity", "lineHeight", "fontWeight", "mt", "fontFamily", "map", "item", "component", "onClick", "selected", "px", "cursor", "mb", "backgroundColor", "min<PERSON><PERSON><PERSON>", "mr", "ml", "badgeContent", "max", "primary", "handleGlobalFeedbackSubmit", "meetingId", "values", "post", "meeting_id", "issue_type", "description", "direction", "position", "width", "zIndex", "xs", "sm", "md", "edge", "noWrap", "flexGrow", "startIcon", "endIcon", "py", "margin", "palette", "common", "white", "transform", "transition", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "paper", "elevation", "overflow", "filter", "content", "top", "right", "height", "src", "profile_picture_url", "startsWith", "alt", "full_name", "border", "borderColor", "char<PERSON>t", "flexShrink", "anchor", "ModalProps", "keepMounted", "boxSizing", "borderRight", "borderLeft", "overflowX", "background", "marginTop", "max<PERSON><PERSON><PERSON>", "isAfter", "datetime", "add", "duration", "meeting", "timezone", "onSubmit", "id", "autoFocus"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/Layout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from '../utils/axios';\nimport {\n  Box,\n  CssBaseline,\n  Drawer,\n  AppBar,\n  Toolbar,\n  List,\n  Typography,\n  Divider,\n  IconButton,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  ListItemButton,\n  Container,\n  useTheme,\n  useMediaQuery,\n  Tooltip,\n  Menu,\n  MenuItem,\n  Avatar,\n  Button,\n  alpha,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  Badge,\n  Link\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  Dashboard as DashboardIcon,\n  Person as PersonIcon,\n  School as SchoolIcon,\n  Chat as ChatIcon,\n  Search as SearchIcon,\n  Category as CategoryIcon,\n  Assignment as AssignmentIcon,\n  Translate as TranslateIcon,\n  KeyboardArrowDown as KeyboardArrowDownIcon,\n  Language as LanguageIcon,\n  Logout as LogoutIcon,\n  AccountCircle as AccountCircleIcon,\n  VideoCall as VideoCallIcon,\n  ContactSupport as ContactSupportIcon,\n  AccountBalanceWallet as AccountBalanceWalletIcon,\n  Payment as PaymentIcon,\n  TrendingUp as TrendingUpIcon,\n  Email as EmailIcon,\n  CalendarMonth as CalendarMonthIcon,\n  Star as StarIcon,\n  RateReview as RateReviewIcon,\n  Edit as EditIcon,\n  AccessTime as AccessTimeIcon,\n  Policy as PolicyIcon,\n  MenuBook as MenuBookIcon,\n  ReportProblem as ReportProblemIcon\n} from '@mui/icons-material';\nimport dayjs from 'dayjs';\nimport utc from 'dayjs/plugin/utc';\n\nimport { useAuth } from '../contexts/AuthContext';\nimport MeetingFeedbackDialog from './MeetingFeedbackDialog';\nimport { useUnreadMessages } from '../contexts/UnreadMessagesContext';\n\ndayjs.extend(utc);\nconst drawerWidth = 240;\n\nconst Layout = ({ children }) => {\n  const [pendingDialogOpen, setPendingDialogOpen] = useState(false);\n  const [pendingIssue, setPendingIssue] = useState(null);\n  const { t, i18n } = useTranslation();\n  const { currentUser, handleLogout } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [langMenuAnchor, setLangMenuAnchor] = useState(null);\n  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);\n  const [balance, setBalance] = useState(null);\n  const { unreadCount } = useUnreadMessages();\n\n  // Helper to fetch pending feedback\n  const fetchPending = async () => {\n    if (currentUser?.role !== 'student') return;\n    try {\n      const { data } = await axios.get('/meeting-issues/pending');\n      if (data.success && data.data) {\n        setPendingIssue(data.data);\n        setPendingDialogOpen(true);\n      }\n    } catch (err) {\n      console.error('Error fetching pending feedback', err);\n    }\n  };\n\n  // Fetch on mount / route change\n  useEffect(() => {\n    fetchPending();\n  }, [currentUser, location.pathname]);\n\n  useEffect(() => {\n    document.dir = isRtl ? 'rtl' : 'ltr';\n  }, [isRtl]);\n\n  useEffect(() => {\n    if (currentUser) {\n      fetchBalance();\n    }\n  }, [currentUser]);\n\n  const fetchBalance = async () => {\n    try {\n      const response = await axios.get('/api/wallet/balance');\n      if (response.data.success) {\n        setBalance(response.data.balance);\n      }\n    } catch (error) {\n      console.error('Error fetching balance:', error);\n    }\n  };\n\n  const handleDrawerToggle = () => {\n    const newState = !mobileOpen;\n    setMobileOpen(newState);\n    if (isMobile) {\n      localStorage.setItem('drawerOpen', newState ? 'true' : 'false');\n    }\n    // Check for pending feedback when drawer opens\n    if (newState) {\n      fetchPending();\n    }\n  };\n\n\n\n  const handleLangMenuOpen = (event) => {\n    setLangMenuAnchor(event.currentTarget);\n  };\n\n  const handleLangMenuClose = () => {\n    setLangMenuAnchor(null);\n  };\n\n  const handleLanguageChange = (lang) => {\n    i18n.changeLanguage(lang);\n    localStorage.setItem('language', lang);\n    handleLangMenuClose();\n  };\n\n  const getMenuItems = () => {\n    if (currentUser?.role === 'platform_teacher') {\n      return [\n        { text: t('menu.dashboard'), icon: <DashboardIcon />, path: '/teacher/dashboard' },\n        { text: t('bookings.title'), icon: <CalendarMonthIcon />, path: '/teacher/bookings' },\n        { text: t('menu.meetings'), icon: <VideoCallIcon />, path: '/teacher/meetings' },\n        { text: t('teacher.myLessons'), icon: <MenuBookIcon />, path: '/teacher/my-lessons' },\n        { text: t('menu.profile'), icon: <PersonIcon />, path: '/teacher/profile' },\n        { text: t('menu.chat'), icon: <ChatIcon />, path: '/teacher/chat', badge: unreadCount },\n        { text: t('reviews.teacherReviews'), icon: <StarIcon />, path: '/teacher/reviews' },\n        { text: t('wallet.title'), icon: <AccountBalanceWalletIcon />, path: '/teacher/wallet' },\n        { text: t('withdrawal.title'), icon: <PaymentIcon />, path: '/teacher/withdrawal' },\n        { text: t('contactUs.title'), icon: <ContactSupportIcon />, path: '/teacher/contact-us' },\n        { text: t('myMessages.title'), icon: <EmailIcon />, path: '/teacher/my-messages' },\n        { text: t('menu.platformPolicy'), icon: <PolicyIcon />, path: '/platform-policy' },\n      ];\n    }\n\n    const adminItems = [\n      { text: t('nav.dashboard'), path: '/admin/dashboard', icon: <DashboardIcon /> },\n      { text: t('nav.applications'), path: '/admin/applications', icon: <AssignmentIcon /> },\n      { text: t('nav.teachers'), path: '/admin/teachers', icon: <SchoolIcon /> },\n      { text: t('nav.students'), path: '/admin/students', icon: <PersonIcon /> },\n      { text: t('nav.profileUpdates'), path: '/admin/profile-updates', icon: <EditIcon /> },\n      { text: t('admin.meetingSessions.title'), path: '/admin/meeting-sessions', icon: <AccessTimeIcon /> },\n      { text: t('admin.meetingIssues.title', 'Meeting Issues'), path: '/admin/meeting-issues', icon: <ReportProblemIcon /> },\n      { text: t('nav.categories'), path: '/admin/categories', icon: <CategoryIcon /> },\n      { text: t('nav.languages'), path: '/admin/languages', icon: <LanguageIcon /> },\n      { text: t('nav.withdrawalManagement'), icon: <PaymentIcon />, path: '/admin/withdrawals' },\n      { text: t('admin.earnings.title'), icon: <TrendingUpIcon />, path: '/admin/earnings' },\n      { text: t('common.profile'), path: '/admin/profile', icon: <PersonIcon /> },\n      { text: t('wallet.title'), path: '/admin/wallet', icon: <AccountBalanceWalletIcon /> },\n      { text: t('admin.messages.title'), path: '/admin/messages', icon: <ContactSupportIcon /> },\n      { text: t('menu.platformPolicy'), path: '/platform-policy', icon: <PolicyIcon /> }\n    ];\n\n    const newTeacherItems = [\n      { text: t('nav.applications'), path: '/teacher/application', icon: <AssignmentIcon /> },\n      { text: t('common.profile'), path: '/teacher/profile', icon: <PersonIcon /> },\n      { text: t('menu.platformPolicy'), path: '/platform-policy', icon: <PolicyIcon /> }\n    ];\n\n    const studentItems = [\n      { text: t('nav.dashboard'), path: '/student/dashboard', icon: <DashboardIcon /> },\n      { text: t('nav.findTeacher'), path: '/student/find-teacher', icon: <SearchIcon /> },\n      { text: t('bookings.title'), path: '/student/bookings', icon: <CalendarMonthIcon /> },\n      { text: t('nav.myTeachers'), path: '/student/my-teachers', icon: <SchoolIcon /> },\n      { text: t('nav.meetings'), path: '/student/meetings', icon: <VideoCallIcon /> },\n      { text: t('nav.chat'), path: '/student/chat', icon: <ChatIcon />, badge: unreadCount },\n      { text: t('reviews.writeReview'), path: '/student/write-review', icon: <RateReviewIcon /> },\n      { text: t('common.profile'), path: '/student/profile', icon: <PersonIcon /> },\n      { text: t('wallet.title'), path: '/student/wallet', icon: <AccountBalanceWalletIcon /> },\n      { text: t('contactUs.title'), path: '/student/contact-us', icon: <ContactSupportIcon /> },\n      { text: t('myMessages.title'), path: '/student/my-messages', icon: <EmailIcon /> },\n      { text: t('menu.platformPolicy'), path: '/platform-policy', icon: <PolicyIcon /> }\n    ];\n\n    switch (currentUser?.role) {\n      case 'admin':\n        return adminItems;\n      case 'new_teacher':\n        return newTeacherItems;\n      case 'student':\n        return studentItems;\n      default:\n        return [];\n    }\n  };\n\n  const handleProfileClick = () => {\n    switch (currentUser?.role) {\n      case 'admin':\n        navigate('/admin/profile');\n        break;\n      case 'platform_teacher':\n      case 'new_teacher':\n        navigate('/teacher/profile');\n        break;\n      case 'student':\n        navigate('/student/profile');\n        break;\n      default:\n        navigate('/login');\n    }\n  };\n\n  const handleLogoutClick = () => {\n    setAnchorEl(null); // Close menu immediately for better UX\n    setLogoutDialogOpen(true);\n  };\n\n  const handleConfirmLogout = async () => {\n    setLogoutDialogOpen(false);\n    try {\n      await handleLogout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  const handleCancelLogout = () => {\n    setLogoutDialogOpen(false);\n  };\n\n  const drawer = (\n    <Box>\n      {/* Balance Display */}\n      {currentUser && balance !== null ? (\n        <Box sx={{\n          mx: 2,\n          my: 2,\n          p: 2,\n          bgcolor: 'primary.light',\n          borderRadius: 2,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: 1.5,\n          minHeight: 64\n        }}>\n          <AccountBalanceWalletIcon sx={{\n            color: 'primary.contrastText',\n            fontSize: '1.5rem'\n          }} />\n          <Box sx={{ textAlign: 'center' }}>\n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: 'primary.contrastText',\n                fontSize: '0.75rem',\n                opacity: 0.9,\n                display: 'block',\n                lineHeight: 1.2\n              }}\n            >\n              {t('wallet.balance')}\n            </Typography>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                color: 'primary.contrastText',\n                fontSize: '1.1rem',\n                fontWeight: 700,\n                lineHeight: 1.2,\n                mt: 0.5\n              }}\n            >\n              ${balance}\n            </Typography>\n          </Box>\n        </Box>\n      ) : (\n        <Box sx={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          p: 2,\n          minHeight: 64\n        }}>\n          <Typography\n            variant=\"h6\"\n            sx={{\n              fontFamily: 'Tajawal, sans-serif',\n              fontWeight: 600,\n              color: 'primary.main'\n            }}\n          >\n            {t('brand.name')}\n          </Typography>\n        </Box>\n      )}\n      <Divider />\n\n      <List>\n        {getMenuItems().map((item) => (\n          <ListItem\n            component=\"div\"\n            key={item.text}\n            onClick={() => {\n              if (location.pathname === item.path) {\n                navigate(0); // Reload current page\n              } else {\n                navigate(item.path);\n              }\n            }}\n            selected={location.pathname === item.path}\n            sx={{\n              minHeight: 48,\n              px: 2.5,\n              cursor: 'pointer',\n              borderRadius: 1,\n              mx: 1,\n              mb: 0.5,\n              '&:hover': {\n                backgroundColor: 'action.hover'\n              },\n              '&.Mui-selected': {\n                backgroundColor: 'primary.main',\n                color: 'primary.contrastText',\n                '&:hover': {\n                  backgroundColor: 'primary.dark'\n                },\n                '& .MuiListItemIcon-root': {\n                  color: 'primary.contrastText'\n                }\n              }\n            }}\n          >\n            <ListItemIcon\n              sx={{\n                minWidth: 0,\n                mr: isRtl ? 0 : 2,\n                ml: isRtl ? 2 : 0,\n                justifyContent: 'center',\n              }}\n            >\n              {item.badge && item.badge > 0 ? (\n                <Badge badgeContent={item.badge} color=\"error\" max={99}>\n                  {item.icon}\n                </Badge>\n              ) : (\n                item.icon\n              )}\n            </ListItemIcon>\n            <ListItemText\n              primary={item.text}\n              sx={{\n                '& .MuiListItemText-primary': {\n                  fontSize: '0.9rem',\n                  fontWeight: 500\n                }\n              }}\n            />\n          </ListItem>\n        ))}\n      </List>\n    </Box>\n  );\n\n  const handleGlobalFeedbackSubmit = async (meetingId, values) => {\n    try {\n      await axios.post('/meeting-issues', {\n        meeting_id: meetingId,\n        issue_type: values.issue_type,\n        description: values.description\n      });\n    } catch (err) {\n      console.error('Failed to submit feedback', err);\n    } finally {\n      setPendingDialogOpen(false);\n      setPendingIssue(null);\n      // Check if there is another pending meeting\n      await fetchPending();\n    }\n  };\n\n  return (\n    <Box sx={{ display: 'flex', direction: isRtl ? 'rtl' : 'ltr' }}>\n      <CssBaseline />\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          width: '100%',\n          zIndex: theme.zIndex.drawer + 1\n        }}\n      >\n        <Toolbar sx={{ minHeight: { xs: 56, sm: 64, md: 70 }, px: { xs: 1, sm: 2, md: 3 } }}>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge={isRtl ? 'end' : 'start'}\n            onClick={handleDrawerToggle}\n            sx={{\n              mr: isRtl ? 0 : { xs: 1, sm: 2 },\n              ml: isRtl ? { xs: 1, sm: 2 } : 0,\n              display: { sm: 'none' },\n              p: { xs: 1, sm: 1.5 }\n            }}\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography\n            variant=\"h6\"\n            noWrap\n            component=\"div\"\n            sx={{\n              flexGrow: 1,\n              fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' },\n              fontFamily: 'Tajawal, sans-serif',\n              fontWeight: { xs: 500, md: 600 }\n            }}\n          >\n            {t('brand.name')}\n          </Typography>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1, md: 1.5 } }}>\n            {/* Language Menu */}\n            <Box>\n              <Button\n                color=\"inherit\"\n                startIcon={<TranslateIcon sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' } }} />}\n                endIcon={<KeyboardArrowDownIcon sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' } }} />}\n                onClick={handleLangMenuOpen}\n                sx={{\n                  minWidth: { xs: 80, sm: 100, md: 120 },\n                  borderRadius: 2,\n                  px: { xs: 1, sm: 1.5, md: 2 },\n                  py: { xs: 0.5, sm: 0.75, md: 1 },\n                  gap: { xs: 0.5, sm: 0.75, md: 1 },\n                  fontSize: { xs: '0.75rem', sm: '0.85rem', md: '0.9rem' },\n                  '& .MuiButton-startIcon': {\n                    margin: 0\n                  },\n                  '& .MuiButton-endIcon': {\n                    margin: 0,\n                    ml: 0.5\n                  },\n                  '&:hover': {\n                    backgroundColor: alpha(theme.palette.common.white, 0.1),\n                    transform: 'translateY(-1px)'\n                  },\n                  transition: 'all 0.2s ease-in-out'\n                }}\n              >\n                {i18n.language === 'ar' ? 'العربية' : 'English'}\n              </Button>\n              <Menu\n                anchorEl={langMenuAnchor}\n                open={Boolean(langMenuAnchor)}\n                onClose={handleLangMenuClose}\n                anchorOrigin={{\n                  vertical: 'bottom',\n                  horizontal: 'right',\n                }}\n                transformOrigin={{\n                  vertical: 'top',\n                  horizontal: 'right',\n                }}\n                slotProps={{\n                  paper: {\n                    elevation: 0,\n                    sx: {\n                      mt: 1.5,\n                      overflow: 'visible',\n                      filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',\n                      '&:before': {\n                        content: '\"\"',\n                        display: 'block',\n                        position: 'absolute',\n                        top: 0,\n                        right: 14,\n                        width: 10,\n                        height: 10,\n                        bgcolor: 'background.paper',\n                        transform: 'translateY(-50%) rotate(45deg)',\n                        zIndex: 0,\n                      },\n                    },\n                  }\n                }}\n              >\n                <MenuItem onClick={() => handleLanguageChange('ar')}>\n                  <ListItemIcon>\n                    <LanguageIcon fontSize=\"small\" />\n                  </ListItemIcon>\n                  العربية\n                </MenuItem>\n                <MenuItem onClick={() => handleLanguageChange('en')}>\n                  <ListItemIcon>\n                    <LanguageIcon fontSize=\"small\" />\n                  </ListItemIcon>\n                  English\n                </MenuItem>\n              </Menu>\n            </Box>\n            {/* User Avatar */}\n            {currentUser && (\n              <>\n                <IconButton\n                  onClick={(event) => setAnchorEl(event.currentTarget)}\n                  sx={{\n                    ml: { xs: 0.5, sm: 1, md: 1.5 },\n                    p: { xs: 0.5, sm: 0.75, md: 1 },\n                    '&:hover': {\n                      transform: 'scale(1.05)',\n                      bgcolor: 'rgba(255,255,255,0.1)'\n                    },\n                    transition: 'all 0.2s ease-in-out'\n                  }}\n                >\n                  <Avatar\n                    src={currentUser.profile_picture_url ? (\n                      currentUser.profile_picture_url.startsWith('http')\n                        ? currentUser.profile_picture_url\n                        : `https://allemnionline.com${currentUser.profile_picture_url}`\n                    ) : ''}\n                    alt={currentUser.full_name}\n                    sx={{\n                      width: { xs: 32, sm: 36, md: 40 },\n                      height: { xs: 32, sm: 36, md: 40 },\n                      bgcolor: 'primary.main',\n                      border: '2px solid',\n                      borderColor: 'background.paper',\n                      fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },\n                      fontWeight: 600\n                    }}\n                  >\n                    {!currentUser.profile_picture_url && currentUser.full_name?.charAt(0)}\n                  </Avatar>\n                </IconButton>\n\n                <Menu\n                  anchorEl={anchorEl}\n                  open={Boolean(anchorEl)}\n                  onClose={() => setAnchorEl(null)}\n                  onClick={() => setAnchorEl(null)}\n                  transformOrigin={{ horizontal: isRtl ? 'left' : 'right', vertical: 'top' }}\n                  anchorOrigin={{ horizontal: isRtl ? 'left' : 'right', vertical: 'bottom' }}\n                >\n                  <MenuItem onClick={handleProfileClick}>\n                    <ListItemIcon>\n                      <PersonIcon fontSize=\"small\" />\n                    </ListItemIcon>\n                    <ListItemText primary={t('common.profile')} />\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={handleLogoutClick}>\n                    <ListItemIcon>\n                      <LogoutIcon fontSize=\"small\" />\n                    </ListItemIcon>\n                    <ListItemText primary={t('auth.logout')} />\n                  </MenuItem>\n                </Menu>\n              </>\n            )}\n\n            {/* Login Button for non-authenticated users */}\n            {!currentUser && (\n              <Button\n                color=\"inherit\"\n                onClick={() => navigate('/login')}\n                sx={{\n                  ml: { xs: 0.5, sm: 1, md: 1.5 },\n                  px: { xs: 1.5, sm: 2, md: 2.5 },\n                  py: { xs: 0.5, sm: 0.75, md: 1 },\n                  fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },\n                  borderRadius: 2,\n                  '&:hover': {\n                    backgroundColor: alpha(theme.palette.common.white, 0.1),\n                    transform: 'translateY(-1px)'\n                  },\n                  transition: 'all 0.2s ease-in-out'\n                }}\n              >\n                {t('auth.login')}\n              </Button>\n            )}\n          </Box>\n        </Toolbar>\n      </AppBar>\n      <Box\n        component=\"nav\"\n        sx={{\n          width: { sm: drawerWidth },\n          flexShrink: { sm: 0 }\n        }}\n      >\n        <Drawer\n          variant={isMobile ? 'temporary' : 'permanent'}\n          open={isMobile ? mobileOpen : true}\n          onClose={handleDrawerToggle}\n          anchor={isRtl ? 'right' : 'left'}\n          ModalProps={{\n            keepMounted: true,\n          }}\n          sx={{\n            '& .MuiDrawer-paper': {\n              boxSizing: 'border-box',\n              width: drawerWidth,\n              top: { xs: '56px', sm: '64px', md: '70px' },\n              height: { xs: 'calc(100vh - 56px)', sm: 'calc(100vh - 64px)', md: 'calc(100vh - 70px)' },\n              borderRight: isRtl ? 'none' : '1px solid rgba(0, 0, 0, 0.12)',\n              borderLeft: isRtl ? '1px solid rgba(0, 0, 0, 0.12)' : 'none',\n              overflowX: 'hidden',\n              position: 'fixed',\n              zIndex: 1000,\n              '&::-webkit-scrollbar': {\n                width: '6px',\n              },\n              '&::-webkit-scrollbar-track': {\n                background: '#f1f1f1',\n              },\n              '&::-webkit-scrollbar-thumb': {\n                background: '#888',\n                borderRadius: '3px',\n              },\n              '&::-webkit-scrollbar-thumb:hover': {\n                background: '#555',\n              },\n            },\n          }}\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: { xs: 0.5, sm: 1, md: 1.5 },\n          width: '100%',\n          marginTop: { xs: '56px', sm: '64px', md: '70px' },\n          minHeight: `calc(100vh - ${isMobile ? '56px' : isTablet ? '64px' : '70px'})`,\n          overflow: 'auto'\n        }}\n      >\n        <Container\n          maxWidth=\"xl\"\n          sx={{\n            px: { xs: 1, sm: 2, md: 3 },\n            py: { xs: 1, sm: 1.5, md: 2 }\n          }}\n        >\n          {children}\n\n      {/* Global Meeting Feedback Dialog */}\n      {currentUser?.role === 'student' && (\n        <MeetingFeedbackDialog\n           open={pendingDialogOpen && pendingIssue && dayjs.utc().isAfter(dayjs.utc(pendingIssue.datetime).add(pendingIssue.duration || 50, 'minute'))}\n           meeting={pendingIssue}\n           timezone={currentUser?.timezone || null}\n           onSubmit={handleGlobalFeedbackSubmit}\n           onClose={null /* Prevent manual close */}\n         />\n      )}\n        </Container>\n      </Box>\n      <Dialog\n        open={logoutDialogOpen}\n        onClose={handleCancelLogout}\n        aria-labelledby=\"logout-dialog-title\"\n        aria-describedby=\"logout-dialog-description\"\n      >\n        <DialogTitle id=\"logout-dialog-title\">\n          {t('auth.logoutConfirmTitle')}\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText id=\"logout-dialog-description\">\n            {t('auth.logoutConfirmMessage')}\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCancelLogout} color=\"primary\">\n            {t('common.cancel')}\n          </Button>\n          <Button onClick={handleConfirmLogout} color=\"primary\" autoFocus>\n            {t('common.confirm')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Layout;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,KAAK,KAAM,gBAAgB,CAClC,OACEC,GAAG,CACHC,WAAW,CACXC,MAAM,CACNC,MAAM,CACNC,OAAO,CACPC,IAAI,CACJC,UAAU,CACVC,OAAO,CACPC,UAAU,CACVC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,aAAa,CACbC,OAAO,CACPC,IAAI,CACJC,QAAQ,CACRC,MAAM,CACNC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,aAAa,CACbC,aAAa,CACbC,iBAAiB,CACjBC,WAAW,CACXC,KAAK,CACLC,IAAI,KACC,eAAe,CACtB,OACEX,IAAI,GAAI,CAAAY,QAAQ,CAChBC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,iBAAiB,GAAI,CAAAC,qBAAqB,CAC1CC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,cAAc,GAAI,CAAAC,kBAAkB,CACpCC,oBAAoB,GAAI,CAAAC,wBAAwB,CAChDC,OAAO,GAAI,CAAAC,WAAW,CACtBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,KAAK,GAAI,CAAAC,SAAS,CAClBC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,aAAa,GAAI,CAAAC,iBAAiB,KAC7B,qBAAqB,CAC5B,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,GAAG,KAAM,kBAAkB,CAElC,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,qBAAqB,KAAM,yBAAyB,CAC3D,OAASC,iBAAiB,KAAQ,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtEV,KAAK,CAACW,MAAM,CAACV,GAAG,CAAC,CACjB,KAAM,CAAAW,WAAW,CAAG,GAAG,CAEvB,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAAkB,KAAAC,qBAAA,IAAjB,CAAEC,QAAS,CAAC,CAAAF,IAAA,CAC1B,KAAM,CAACG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1G,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC2G,YAAY,CAAEC,eAAe,CAAC,CAAG5G,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAE6G,CAAC,CAAEC,IAAK,CAAC,CAAG5G,cAAc,CAAC,CAAC,CACpC,KAAM,CAAE6G,WAAW,CAAEC,YAAa,CAAC,CAAGtB,OAAO,CAAC,CAAC,CAC/C,KAAM,CAAAuB,QAAQ,CAAG9G,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+G,QAAQ,CAAG9G,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+G,KAAK,CAAG/F,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAgG,KAAK,CAAGN,IAAI,CAACO,QAAQ,GAAK,IAAI,CACpC,KAAM,CAAAC,QAAQ,CAAGjG,aAAa,CAAC8F,KAAK,CAACI,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAAAC,QAAQ,CAAGpG,aAAa,CAAC8F,KAAK,CAACI,WAAW,CAACG,OAAO,CAAC,IAAI,CAAE,IAAI,CAAC,CAAC,CACrE,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG5H,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC6H,QAAQ,CAAEC,WAAW,CAAC,CAAG9H,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAAC+H,cAAc,CAAEC,iBAAiB,CAAC,CAAGhI,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACiI,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlI,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACmI,OAAO,CAAEC,UAAU,CAAC,CAAGpI,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAEqI,WAAY,CAAC,CAAGzC,iBAAiB,CAAC,CAAC,CAE3C;AACA,KAAM,CAAA0C,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAAAvB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,IAAK,SAAS,CAAE,OACrC,GAAI,CACF,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAAnI,KAAK,CAACoI,GAAG,CAAC,yBAAyB,CAAC,CAC3D,GAAID,IAAI,CAACE,OAAO,EAAIF,IAAI,CAACA,IAAI,CAAE,CAC7B5B,eAAe,CAAC4B,IAAI,CAACA,IAAI,CAAC,CAC1B9B,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CACF,CAAE,MAAOiC,GAAG,CAAE,CACZC,OAAO,CAACC,KAAK,CAAC,iCAAiC,CAAEF,GAAG,CAAC,CACvD,CACF,CAAC,CAED;AACA1I,SAAS,CAAC,IAAM,CACdqI,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,CAACvB,WAAW,CAAEG,QAAQ,CAAC4B,QAAQ,CAAC,CAAC,CAEpC7I,SAAS,CAAC,IAAM,CACd8I,QAAQ,CAACC,GAAG,CAAG5B,KAAK,CAAG,KAAK,CAAG,KAAK,CACtC,CAAC,CAAE,CAACA,KAAK,CAAC,CAAC,CAEXnH,SAAS,CAAC,IAAM,CACd,GAAI8G,WAAW,CAAE,CACfkC,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAAE,CAAClC,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAAkC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA7I,KAAK,CAACoI,GAAG,CAAC,qBAAqB,CAAC,CACvD,GAAIS,QAAQ,CAACV,IAAI,CAACE,OAAO,CAAE,CACzBN,UAAU,CAACc,QAAQ,CAACV,IAAI,CAACL,OAAO,CAAC,CACnC,CACF,CAAE,MAAOU,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAM,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,QAAQ,CAAG,CAACzB,UAAU,CAC5BC,aAAa,CAACwB,QAAQ,CAAC,CACvB,GAAI9B,QAAQ,CAAE,CACZ+B,YAAY,CAACC,OAAO,CAAC,YAAY,CAAEF,QAAQ,CAAG,MAAM,CAAG,OAAO,CAAC,CACjE,CACA;AACA,GAAIA,QAAQ,CAAE,CACZd,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAID,KAAM,CAAAiB,kBAAkB,CAAIC,KAAK,EAAK,CACpCxB,iBAAiB,CAACwB,KAAK,CAACC,aAAa,CAAC,CACxC,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChC1B,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAA2B,oBAAoB,CAAIC,IAAI,EAAK,CACrC9C,IAAI,CAAC+C,cAAc,CAACD,IAAI,CAAC,CACzBP,YAAY,CAACC,OAAO,CAAC,UAAU,CAAEM,IAAI,CAAC,CACtCF,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAI,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAAA/C,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,IAAK,kBAAkB,CAAE,CAC5C,MAAO,CACL,CAAEwB,IAAI,CAAElD,CAAC,CAAC,gBAAgB,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACzD,aAAa,GAAE,CAAC,CAAE4H,IAAI,CAAE,oBAAqB,CAAC,CAClF,CAAEF,IAAI,CAAElD,CAAC,CAAC,gBAAgB,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACrB,iBAAiB,GAAE,CAAC,CAAEwF,IAAI,CAAE,mBAAoB,CAAC,CACrF,CAAEF,IAAI,CAAElD,CAAC,CAAC,eAAe,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACjC,aAAa,GAAE,CAAC,CAAEoG,IAAI,CAAE,mBAAoB,CAAC,CAChF,CAAEF,IAAI,CAAElD,CAAC,CAAC,mBAAmB,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACT,YAAY,GAAE,CAAC,CAAE4E,IAAI,CAAE,qBAAsB,CAAC,CACrF,CAAEF,IAAI,CAAElD,CAAC,CAAC,cAAc,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACvD,UAAU,GAAE,CAAC,CAAE0H,IAAI,CAAE,kBAAmB,CAAC,CAC3E,CAAEF,IAAI,CAAElD,CAAC,CAAC,WAAW,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACnD,QAAQ,GAAE,CAAC,CAAEsH,IAAI,CAAE,eAAe,CAAEC,KAAK,CAAE7B,WAAY,CAAC,CACvF,CAAE0B,IAAI,CAAElD,CAAC,CAAC,wBAAwB,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACnB,QAAQ,GAAE,CAAC,CAAEsF,IAAI,CAAE,kBAAmB,CAAC,CACnF,CAAEF,IAAI,CAAElD,CAAC,CAAC,cAAc,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAAC7B,wBAAwB,GAAE,CAAC,CAAEgG,IAAI,CAAE,iBAAkB,CAAC,CACxF,CAAEF,IAAI,CAAElD,CAAC,CAAC,kBAAkB,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAAC3B,WAAW,GAAE,CAAC,CAAE8F,IAAI,CAAE,qBAAsB,CAAC,CACnF,CAAEF,IAAI,CAAElD,CAAC,CAAC,iBAAiB,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAAC/B,kBAAkB,GAAE,CAAC,CAAEkG,IAAI,CAAE,qBAAsB,CAAC,CACzF,CAAEF,IAAI,CAAElD,CAAC,CAAC,kBAAkB,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACvB,SAAS,GAAE,CAAC,CAAE0F,IAAI,CAAE,sBAAuB,CAAC,CAClF,CAAEF,IAAI,CAAElD,CAAC,CAAC,qBAAqB,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACX,UAAU,GAAE,CAAC,CAAE8E,IAAI,CAAE,kBAAmB,CAAC,CACnF,CACH,CAEA,KAAM,CAAAE,UAAU,CAAG,CACjB,CAAEJ,IAAI,CAAElD,CAAC,CAAC,eAAe,CAAC,CAAEoD,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAElE,IAAA,CAACzD,aAAa,GAAE,CAAE,CAAC,CAC/E,CAAE0H,IAAI,CAAElD,CAAC,CAAC,kBAAkB,CAAC,CAAEoD,IAAI,CAAE,qBAAqB,CAAED,IAAI,cAAElE,IAAA,CAAC7C,cAAc,GAAE,CAAE,CAAC,CACtF,CAAE8G,IAAI,CAAElD,CAAC,CAAC,cAAc,CAAC,CAAEoD,IAAI,CAAE,iBAAiB,CAAED,IAAI,cAAElE,IAAA,CAACrD,UAAU,GAAE,CAAE,CAAC,CAC1E,CAAEsH,IAAI,CAAElD,CAAC,CAAC,cAAc,CAAC,CAAEoD,IAAI,CAAE,iBAAiB,CAAED,IAAI,cAAElE,IAAA,CAACvD,UAAU,GAAE,CAAE,CAAC,CAC1E,CAAEwH,IAAI,CAAElD,CAAC,CAAC,oBAAoB,CAAC,CAAEoD,IAAI,CAAE,wBAAwB,CAAED,IAAI,cAAElE,IAAA,CAACf,QAAQ,GAAE,CAAE,CAAC,CACrF,CAAEgF,IAAI,CAAElD,CAAC,CAAC,6BAA6B,CAAC,CAAEoD,IAAI,CAAE,yBAAyB,CAAED,IAAI,cAAElE,IAAA,CAACb,cAAc,GAAE,CAAE,CAAC,CACrG,CAAE8E,IAAI,CAAElD,CAAC,CAAC,2BAA2B,CAAE,gBAAgB,CAAC,CAAEoD,IAAI,CAAE,uBAAuB,CAAED,IAAI,cAAElE,IAAA,CAACP,iBAAiB,GAAE,CAAE,CAAC,CACtH,CAAEwE,IAAI,CAAElD,CAAC,CAAC,gBAAgB,CAAC,CAAEoD,IAAI,CAAE,mBAAmB,CAAED,IAAI,cAAElE,IAAA,CAAC/C,YAAY,GAAE,CAAE,CAAC,CAChF,CAAEgH,IAAI,CAAElD,CAAC,CAAC,eAAe,CAAC,CAAEoD,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAElE,IAAA,CAACvC,YAAY,GAAE,CAAE,CAAC,CAC9E,CAAEwG,IAAI,CAAElD,CAAC,CAAC,0BAA0B,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAAC3B,WAAW,GAAE,CAAC,CAAE8F,IAAI,CAAE,oBAAqB,CAAC,CAC1F,CAAEF,IAAI,CAAElD,CAAC,CAAC,sBAAsB,CAAC,CAAEmD,IAAI,cAAElE,IAAA,CAACzB,cAAc,GAAE,CAAC,CAAE4F,IAAI,CAAE,iBAAkB,CAAC,CACtF,CAAEF,IAAI,CAAElD,CAAC,CAAC,gBAAgB,CAAC,CAAEoD,IAAI,CAAE,gBAAgB,CAAED,IAAI,cAAElE,IAAA,CAACvD,UAAU,GAAE,CAAE,CAAC,CAC3E,CAAEwH,IAAI,CAAElD,CAAC,CAAC,cAAc,CAAC,CAAEoD,IAAI,CAAE,eAAe,CAAED,IAAI,cAAElE,IAAA,CAAC7B,wBAAwB,GAAE,CAAE,CAAC,CACtF,CAAE8F,IAAI,CAAElD,CAAC,CAAC,sBAAsB,CAAC,CAAEoD,IAAI,CAAE,iBAAiB,CAAED,IAAI,cAAElE,IAAA,CAAC/B,kBAAkB,GAAE,CAAE,CAAC,CAC1F,CAAEgG,IAAI,CAAElD,CAAC,CAAC,qBAAqB,CAAC,CAAEoD,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAElE,IAAA,CAACX,UAAU,GAAE,CAAE,CAAC,CACnF,CAED,KAAM,CAAAiF,eAAe,CAAG,CACtB,CAAEL,IAAI,CAAElD,CAAC,CAAC,kBAAkB,CAAC,CAAEoD,IAAI,CAAE,sBAAsB,CAAED,IAAI,cAAElE,IAAA,CAAC7C,cAAc,GAAE,CAAE,CAAC,CACvF,CAAE8G,IAAI,CAAElD,CAAC,CAAC,gBAAgB,CAAC,CAAEoD,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAElE,IAAA,CAACvD,UAAU,GAAE,CAAE,CAAC,CAC7E,CAAEwH,IAAI,CAAElD,CAAC,CAAC,qBAAqB,CAAC,CAAEoD,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAElE,IAAA,CAACX,UAAU,GAAE,CAAE,CAAC,CACnF,CAED,KAAM,CAAAkF,YAAY,CAAG,CACnB,CAAEN,IAAI,CAAElD,CAAC,CAAC,eAAe,CAAC,CAAEoD,IAAI,CAAE,oBAAoB,CAAED,IAAI,cAAElE,IAAA,CAACzD,aAAa,GAAE,CAAE,CAAC,CACjF,CAAE0H,IAAI,CAAElD,CAAC,CAAC,iBAAiB,CAAC,CAAEoD,IAAI,CAAE,uBAAuB,CAAED,IAAI,cAAElE,IAAA,CAACjD,UAAU,GAAE,CAAE,CAAC,CACnF,CAAEkH,IAAI,CAAElD,CAAC,CAAC,gBAAgB,CAAC,CAAEoD,IAAI,CAAE,mBAAmB,CAAED,IAAI,cAAElE,IAAA,CAACrB,iBAAiB,GAAE,CAAE,CAAC,CACrF,CAAEsF,IAAI,CAAElD,CAAC,CAAC,gBAAgB,CAAC,CAAEoD,IAAI,CAAE,sBAAsB,CAAED,IAAI,cAAElE,IAAA,CAACrD,UAAU,GAAE,CAAE,CAAC,CACjF,CAAEsH,IAAI,CAAElD,CAAC,CAAC,cAAc,CAAC,CAAEoD,IAAI,CAAE,mBAAmB,CAAED,IAAI,cAAElE,IAAA,CAACjC,aAAa,GAAE,CAAE,CAAC,CAC/E,CAAEkG,IAAI,CAAElD,CAAC,CAAC,UAAU,CAAC,CAAEoD,IAAI,CAAE,eAAe,CAAED,IAAI,cAAElE,IAAA,CAACnD,QAAQ,GAAE,CAAC,CAAEuH,KAAK,CAAE7B,WAAY,CAAC,CACtF,CAAE0B,IAAI,CAAElD,CAAC,CAAC,qBAAqB,CAAC,CAAEoD,IAAI,CAAE,uBAAuB,CAAED,IAAI,cAAElE,IAAA,CAACjB,cAAc,GAAE,CAAE,CAAC,CAC3F,CAAEkF,IAAI,CAAElD,CAAC,CAAC,gBAAgB,CAAC,CAAEoD,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAElE,IAAA,CAACvD,UAAU,GAAE,CAAE,CAAC,CAC7E,CAAEwH,IAAI,CAAElD,CAAC,CAAC,cAAc,CAAC,CAAEoD,IAAI,CAAE,iBAAiB,CAAED,IAAI,cAAElE,IAAA,CAAC7B,wBAAwB,GAAE,CAAE,CAAC,CACxF,CAAE8F,IAAI,CAAElD,CAAC,CAAC,iBAAiB,CAAC,CAAEoD,IAAI,CAAE,qBAAqB,CAAED,IAAI,cAAElE,IAAA,CAAC/B,kBAAkB,GAAE,CAAE,CAAC,CACzF,CAAEgG,IAAI,CAAElD,CAAC,CAAC,kBAAkB,CAAC,CAAEoD,IAAI,CAAE,sBAAsB,CAAED,IAAI,cAAElE,IAAA,CAACvB,SAAS,GAAE,CAAE,CAAC,CAClF,CAAEwF,IAAI,CAAElD,CAAC,CAAC,qBAAqB,CAAC,CAAEoD,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAElE,IAAA,CAACX,UAAU,GAAE,CAAE,CAAC,CACnF,CAED,OAAQ4B,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,EACvB,IAAK,OAAO,CACV,MAAO,CAAA4B,UAAU,CACnB,IAAK,aAAa,CAChB,MAAO,CAAAC,eAAe,CACxB,IAAK,SAAS,CACZ,MAAO,CAAAC,YAAY,CACrB,QACE,MAAO,EAAE,CACb,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,OAAQvD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,EACvB,IAAK,OAAO,CACVtB,QAAQ,CAAC,gBAAgB,CAAC,CAC1B,MACF,IAAK,kBAAkB,CACvB,IAAK,aAAa,CAChBA,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,IAAK,SAAS,CACZA,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,QACEA,QAAQ,CAAC,QAAQ,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAsD,iBAAiB,CAAGA,CAAA,GAAM,CAC9BzC,WAAW,CAAC,IAAI,CAAC,CAAE;AACnBI,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAsC,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtCtC,mBAAmB,CAAC,KAAK,CAAC,CAC1B,GAAI,CACF,KAAM,CAAAlB,YAAY,CAAC,CAAC,CACtB,CAAE,MAAO6B,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACxC,CACF,CAAC,CAED,KAAM,CAAA4B,kBAAkB,CAAGA,CAAA,GAAM,CAC/BvC,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAwC,MAAM,cACV1E,KAAA,CAAC1F,GAAG,EAAAkG,QAAA,EAEDO,WAAW,EAAIoB,OAAO,GAAK,IAAI,cAC9BnC,KAAA,CAAC1F,GAAG,EAACqK,EAAE,CAAE,CACPC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CAAC,CACLC,CAAC,CAAE,CAAC,CACJC,OAAO,CAAE,eAAe,CACxBC,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,GAAG,CAAE,GAAG,CACRC,SAAS,CAAE,EACb,CAAE,CAAA7E,QAAA,eACAV,IAAA,CAAC7B,wBAAwB,EAAC0G,EAAE,CAAE,CAC5BW,KAAK,CAAE,sBAAsB,CAC7BC,QAAQ,CAAE,QACZ,CAAE,CAAE,CAAC,cACLvF,KAAA,CAAC1F,GAAG,EAACqK,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAS,CAAE,CAAAhF,QAAA,eAC/BV,IAAA,CAAClF,UAAU,EACT6K,OAAO,CAAC,SAAS,CACjBd,EAAE,CAAE,CACFW,KAAK,CAAE,sBAAsB,CAC7BC,QAAQ,CAAE,SAAS,CACnBG,OAAO,CAAE,GAAG,CACZT,OAAO,CAAE,OAAO,CAChBU,UAAU,CAAE,GACd,CAAE,CAAAnF,QAAA,CAEDK,CAAC,CAAC,gBAAgB,CAAC,CACV,CAAC,cACbb,KAAA,CAACpF,UAAU,EACT6K,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFW,KAAK,CAAE,sBAAsB,CAC7BC,QAAQ,CAAE,QAAQ,CAClBK,UAAU,CAAE,GAAG,CACfD,UAAU,CAAE,GAAG,CACfE,EAAE,CAAE,GACN,CAAE,CAAArF,QAAA,EACH,GACE,CAAC2B,OAAO,EACC,CAAC,EACV,CAAC,EACH,CAAC,cAENrC,IAAA,CAACxF,GAAG,EAACqK,EAAE,CAAE,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBL,CAAC,CAAE,CAAC,CACJO,SAAS,CAAE,EACb,CAAE,CAAA7E,QAAA,cACAV,IAAA,CAAClF,UAAU,EACT6K,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFmB,UAAU,CAAE,qBAAqB,CACjCF,UAAU,CAAE,GAAG,CACfN,KAAK,CAAE,cACT,CAAE,CAAA9E,QAAA,CAEDK,CAAC,CAAC,YAAY,CAAC,CACN,CAAC,CACV,CACN,cACDf,IAAA,CAACjF,OAAO,GAAE,CAAC,cAEXiF,IAAA,CAACnF,IAAI,EAAA6F,QAAA,CACFsD,YAAY,CAAC,CAAC,CAACiC,GAAG,CAAEC,IAAI,eACvBhG,KAAA,CAACjF,QAAQ,EACPkL,SAAS,CAAC,KAAK,CAEfC,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIhF,QAAQ,CAAC4B,QAAQ,GAAKkD,IAAI,CAAC/B,IAAI,CAAE,CACnChD,QAAQ,CAAC,CAAC,CAAC,CAAE;AACf,CAAC,IAAM,CACLA,QAAQ,CAAC+E,IAAI,CAAC/B,IAAI,CAAC,CACrB,CACF,CAAE,CACFkC,QAAQ,CAAEjF,QAAQ,CAAC4B,QAAQ,GAAKkD,IAAI,CAAC/B,IAAK,CAC1CU,EAAE,CAAE,CACFU,SAAS,CAAE,EAAE,CACbe,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,SAAS,CACjBrB,YAAY,CAAE,CAAC,CACfJ,EAAE,CAAE,CAAC,CACL0B,EAAE,CAAE,GAAG,CACP,SAAS,CAAE,CACTC,eAAe,CAAE,cACnB,CAAC,CACD,gBAAgB,CAAE,CAChBA,eAAe,CAAE,cAAc,CAC/BjB,KAAK,CAAE,sBAAsB,CAC7B,SAAS,CAAE,CACTiB,eAAe,CAAE,cACnB,CAAC,CACD,yBAAyB,CAAE,CACzBjB,KAAK,CAAE,sBACT,CACF,CACF,CAAE,CAAA9E,QAAA,eAEFV,IAAA,CAAC9E,YAAY,EACX2J,EAAE,CAAE,CACF6B,QAAQ,CAAE,CAAC,CACXC,EAAE,CAAErF,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBsF,EAAE,CAAEtF,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB+D,cAAc,CAAE,QAClB,CAAE,CAAA3E,QAAA,CAEDwF,IAAI,CAAC9B,KAAK,EAAI8B,IAAI,CAAC9B,KAAK,CAAG,CAAC,cAC3BpE,IAAA,CAAC7D,KAAK,EAAC0K,YAAY,CAAEX,IAAI,CAAC9B,KAAM,CAACoB,KAAK,CAAC,OAAO,CAACsB,GAAG,CAAE,EAAG,CAAApG,QAAA,CACpDwF,IAAI,CAAChC,IAAI,CACL,CAAC,CAERgC,IAAI,CAAChC,IACN,CACW,CAAC,cACflE,IAAA,CAAC7E,YAAY,EACX4L,OAAO,CAAEb,IAAI,CAACjC,IAAK,CACnBY,EAAE,CAAE,CACF,4BAA4B,CAAE,CAC5BY,QAAQ,CAAE,QAAQ,CAClBK,UAAU,CAAE,GACd,CACF,CAAE,CACH,CAAC,GAvDGI,IAAI,CAACjC,IAwDF,CACX,CAAC,CACE,CAAC,EACJ,CACN,CAED,KAAM,CAAA+C,0BAA0B,CAAG,KAAAA,CAAOC,SAAS,CAAEC,MAAM,GAAK,CAC9D,GAAI,CACF,KAAM,CAAA3M,KAAK,CAAC4M,IAAI,CAAC,iBAAiB,CAAE,CAClCC,UAAU,CAAEH,SAAS,CACrBI,UAAU,CAAEH,MAAM,CAACG,UAAU,CAC7BC,WAAW,CAAEJ,MAAM,CAACI,WACtB,CAAC,CAAC,CACJ,CAAE,MAAOzE,GAAG,CAAE,CACZC,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAEF,GAAG,CAAC,CACjD,CAAC,OAAS,CACRjC,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,eAAe,CAAC,IAAI,CAAC,CACrB;AACA,KAAM,CAAA0B,YAAY,CAAC,CAAC,CACtB,CACF,CAAC,CAED,mBACEtC,KAAA,CAAC1F,GAAG,EAACqK,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEoC,SAAS,CAAEjG,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAZ,QAAA,eAC7DV,IAAA,CAACvF,WAAW,GAAE,CAAC,cACfuF,IAAA,CAACrF,MAAM,EACL6M,QAAQ,CAAC,OAAO,CAChB3C,EAAE,CAAE,CACF4C,KAAK,CAAE,MAAM,CACbC,MAAM,CAAErG,KAAK,CAACqG,MAAM,CAAC9C,MAAM,CAAG,CAChC,CAAE,CAAAlE,QAAA,cAEFR,KAAA,CAACtF,OAAO,EAACiK,EAAE,CAAE,CAAEU,SAAS,CAAE,CAAEoC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAAEvB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAAnH,QAAA,eAClFV,IAAA,CAAChF,UAAU,EACTwK,KAAK,CAAC,SAAS,CACf,aAAW,aAAa,CACxBsC,IAAI,CAAExG,KAAK,CAAG,KAAK,CAAG,OAAQ,CAC9B8E,OAAO,CAAE/C,kBAAmB,CAC5BwB,EAAE,CAAE,CACF8B,EAAE,CAAErF,KAAK,CAAG,CAAC,CAAG,CAAEqG,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAChChB,EAAE,CAAEtF,KAAK,CAAG,CAAEqG,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAG,CAAC,CAChCzC,OAAO,CAAE,CAAEyC,EAAE,CAAE,MAAO,CAAC,CACvB5C,CAAC,CAAE,CAAE2C,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CACtB,CAAE,CAAAlH,QAAA,cAEFV,IAAA,CAAC3D,QAAQ,GAAE,CAAC,CACF,CAAC,cACb2D,IAAA,CAAClF,UAAU,EACT6K,OAAO,CAAC,IAAI,CACZoC,MAAM,MACN5B,SAAS,CAAC,KAAK,CACftB,EAAE,CAAE,CACFmD,QAAQ,CAAE,CAAC,CACXvC,QAAQ,CAAE,CAAEkC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,SAAU,CAAC,CACrD7B,UAAU,CAAE,qBAAqB,CACjCF,UAAU,CAAE,CAAE6B,EAAE,CAAE,GAAG,CAAEE,EAAE,CAAE,GAAI,CACjC,CAAE,CAAAnH,QAAA,CAEDK,CAAC,CAAC,YAAY,CAAC,CACN,CAAC,cACbb,KAAA,CAAC1F,GAAG,EAACqK,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEE,GAAG,CAAE,CAAEqC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAE,CAAAnH,QAAA,eAEnFR,KAAA,CAAC1F,GAAG,EAAAkG,QAAA,eACFV,IAAA,CAACpE,MAAM,EACL4J,KAAK,CAAC,SAAS,CACfyC,SAAS,cAAEjI,IAAA,CAAC3C,aAAa,EAACwH,EAAE,CAAE,CAAEY,QAAQ,CAAE,CAAEkC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAE,CAAE,CAAE,CAAE,CAC3FK,OAAO,cAAElI,IAAA,CAACzC,qBAAqB,EAACsH,EAAE,CAAE,CAAEY,QAAQ,CAAE,CAAEkC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAE,CAAE,CAAE,CAAE,CACjGzB,OAAO,CAAE3C,kBAAmB,CAC5BoB,EAAE,CAAE,CACF6B,QAAQ,CAAE,CAAEiB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CACtC3C,YAAY,CAAE,CAAC,CACfoB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC7BM,EAAE,CAAE,CAAER,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,IAAI,CAAEC,EAAE,CAAE,CAAE,CAAC,CAChCvC,GAAG,CAAE,CAAEqC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,IAAI,CAAEC,EAAE,CAAE,CAAE,CAAC,CACjCpC,QAAQ,CAAE,CAAEkC,EAAE,CAAE,SAAS,CAAEC,EAAE,CAAE,SAAS,CAAEC,EAAE,CAAE,QAAS,CAAC,CACxD,wBAAwB,CAAE,CACxBO,MAAM,CAAE,CACV,CAAC,CACD,sBAAsB,CAAE,CACtBA,MAAM,CAAE,CAAC,CACTxB,EAAE,CAAE,GACN,CAAC,CACD,SAAS,CAAE,CACTH,eAAe,CAAE5K,KAAK,CAACwF,KAAK,CAACgH,OAAO,CAACC,MAAM,CAACC,KAAK,CAAE,GAAG,CAAC,CACvDC,SAAS,CAAE,kBACb,CAAC,CACDC,UAAU,CAAE,sBACd,CAAE,CAAA/H,QAAA,CAEDM,IAAI,CAACO,QAAQ,GAAK,IAAI,CAAG,SAAS,CAAG,SAAS,CACzC,CAAC,cACTrB,KAAA,CAACzE,IAAI,EACHsG,QAAQ,CAAEE,cAAe,CACzByG,IAAI,CAAEC,OAAO,CAAC1G,cAAc,CAAE,CAC9B2G,OAAO,CAAEhF,mBAAoB,CAC7BiF,YAAY,CAAE,CACZC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,OACd,CAAE,CACFC,eAAe,CAAE,CACfF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,OACd,CAAE,CACFE,SAAS,CAAE,CACTC,KAAK,CAAE,CACLC,SAAS,CAAE,CAAC,CACZtE,EAAE,CAAE,CACFkB,EAAE,CAAE,GAAG,CACPqD,QAAQ,CAAE,SAAS,CACnBC,MAAM,CAAE,2CAA2C,CACnD,UAAU,CAAE,CACVC,OAAO,CAAE,IAAI,CACbnE,OAAO,CAAE,OAAO,CAChBqC,QAAQ,CAAE,UAAU,CACpB+B,GAAG,CAAE,CAAC,CACNC,KAAK,CAAE,EAAE,CACT/B,KAAK,CAAE,EAAE,CACTgC,MAAM,CAAE,EAAE,CACVxE,OAAO,CAAE,kBAAkB,CAC3BuD,SAAS,CAAE,gCAAgC,CAC3Cd,MAAM,CAAE,CACV,CACF,CACF,CACF,CAAE,CAAAhH,QAAA,eAEFR,KAAA,CAACxE,QAAQ,EAAC0K,OAAO,CAAEA,CAAA,GAAMvC,oBAAoB,CAAC,IAAI,CAAE,CAAAnD,QAAA,eAClDV,IAAA,CAAC9E,YAAY,EAAAwF,QAAA,cACXV,IAAA,CAACvC,YAAY,EAACgI,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrB,CAAC,6CAEjB,EAAU,CAAC,cACXvF,KAAA,CAACxE,QAAQ,EAAC0K,OAAO,CAAEA,CAAA,GAAMvC,oBAAoB,CAAC,IAAI,CAAE,CAAAnD,QAAA,eAClDV,IAAA,CAAC9E,YAAY,EAAAwF,QAAA,cACXV,IAAA,CAACvC,YAAY,EAACgI,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrB,CAAC,UAEjB,EAAU,CAAC,EACP,CAAC,EACJ,CAAC,CAELxE,WAAW,eACVf,KAAA,CAAAE,SAAA,EAAAM,QAAA,eACEV,IAAA,CAAChF,UAAU,EACToL,OAAO,CAAG1C,KAAK,EAAK1B,WAAW,CAAC0B,KAAK,CAACC,aAAa,CAAE,CACrDkB,EAAE,CAAE,CACF+B,EAAE,CAAE,CAAEe,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC/B7C,CAAC,CAAE,CAAE2C,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,IAAI,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC/B,SAAS,CAAE,CACTW,SAAS,CAAE,aAAa,CACxBvD,OAAO,CAAE,uBACX,CAAC,CACDwD,UAAU,CAAE,sBACd,CAAE,CAAA/H,QAAA,cAEFV,IAAA,CAACrE,MAAM,EACL+N,GAAG,CAAEzI,WAAW,CAAC0I,mBAAmB,CAClC1I,WAAW,CAAC0I,mBAAmB,CAACC,UAAU,CAAC,MAAM,CAAC,CAC9C3I,WAAW,CAAC0I,mBAAmB,CAC/B,4BAA4B1I,WAAW,CAAC0I,mBAAmB,EAAE,CAC/D,EAAG,CACPE,GAAG,CAAE5I,WAAW,CAAC6I,SAAU,CAC3BjF,EAAE,CAAE,CACF4C,KAAK,CAAE,CAAEE,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CACjC4B,MAAM,CAAE,CAAE9B,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAClC5C,OAAO,CAAE,cAAc,CACvB8E,MAAM,CAAE,WAAW,CACnBC,WAAW,CAAE,kBAAkB,CAC/BvE,QAAQ,CAAE,CAAEkC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACpD/B,UAAU,CAAE,GACd,CAAE,CAAApF,QAAA,CAED,CAACO,WAAW,CAAC0I,mBAAmB,IAAAlJ,qBAAA,CAAIQ,WAAW,CAAC6I,SAAS,UAAArJ,qBAAA,iBAArBA,qBAAA,CAAuBwJ,MAAM,CAAC,CAAC,CAAC,EAC/D,CAAC,CACC,CAAC,cAEb/J,KAAA,CAACzE,IAAI,EACHsG,QAAQ,CAAEA,QAAS,CACnB2G,IAAI,CAAEC,OAAO,CAAC5G,QAAQ,CAAE,CACxB6G,OAAO,CAAEA,CAAA,GAAM5G,WAAW,CAAC,IAAI,CAAE,CACjCoE,OAAO,CAAEA,CAAA,GAAMpE,WAAW,CAAC,IAAI,CAAE,CACjCgH,eAAe,CAAE,CAAED,UAAU,CAAEzH,KAAK,CAAG,MAAM,CAAG,OAAO,CAAEwH,QAAQ,CAAE,KAAM,CAAE,CAC3ED,YAAY,CAAE,CAAEE,UAAU,CAAEzH,KAAK,CAAG,MAAM,CAAG,OAAO,CAAEwH,QAAQ,CAAE,QAAS,CAAE,CAAApI,QAAA,eAE3ER,KAAA,CAACxE,QAAQ,EAAC0K,OAAO,CAAE5B,kBAAmB,CAAA9D,QAAA,eACpCV,IAAA,CAAC9E,YAAY,EAAAwF,QAAA,cACXV,IAAA,CAACvD,UAAU,EAACgJ,QAAQ,CAAC,OAAO,CAAE,CAAC,CACnB,CAAC,cACfzF,IAAA,CAAC7E,YAAY,EAAC4L,OAAO,CAAEhG,CAAC,CAAC,gBAAgB,CAAE,CAAE,CAAC,EACtC,CAAC,cACXf,IAAA,CAACjF,OAAO,GAAE,CAAC,cACXmF,KAAA,CAACxE,QAAQ,EAAC0K,OAAO,CAAE3B,iBAAkB,CAAA/D,QAAA,eACnCV,IAAA,CAAC9E,YAAY,EAAAwF,QAAA,cACXV,IAAA,CAACrC,UAAU,EAAC8H,QAAQ,CAAC,OAAO,CAAE,CAAC,CACnB,CAAC,cACfzF,IAAA,CAAC7E,YAAY,EAAC4L,OAAO,CAAEhG,CAAC,CAAC,aAAa,CAAE,CAAE,CAAC,EACnC,CAAC,EACP,CAAC,EACP,CACH,CAGA,CAACE,WAAW,eACXjB,IAAA,CAACpE,MAAM,EACL4J,KAAK,CAAC,SAAS,CACfY,OAAO,CAAEA,CAAA,GAAMjF,QAAQ,CAAC,QAAQ,CAAE,CAClC0D,EAAE,CAAE,CACF+B,EAAE,CAAE,CAAEe,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC/BvB,EAAE,CAAE,CAAEqB,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC/BM,EAAE,CAAE,CAAER,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,IAAI,CAAEC,EAAE,CAAE,CAAE,CAAC,CAChCpC,QAAQ,CAAE,CAAEkC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAO,CAAC,CACpD3C,YAAY,CAAE,CAAC,CACf,SAAS,CAAE,CACTuB,eAAe,CAAE5K,KAAK,CAACwF,KAAK,CAACgH,OAAO,CAACC,MAAM,CAACC,KAAK,CAAE,GAAG,CAAC,CACvDC,SAAS,CAAE,kBACb,CAAC,CACDC,UAAU,CAAE,sBACd,CAAE,CAAA/H,QAAA,CAEDK,CAAC,CAAC,YAAY,CAAC,CACV,CACT,EACE,CAAC,EACC,CAAC,CACJ,CAAC,cACTf,IAAA,CAACxF,GAAG,EACF2L,SAAS,CAAC,KAAK,CACftB,EAAE,CAAE,CACF4C,KAAK,CAAE,CAAEG,EAAE,CAAEtH,WAAY,CAAC,CAC1B4J,UAAU,CAAE,CAAEtC,EAAE,CAAE,CAAE,CACtB,CAAE,CAAAlH,QAAA,cAEFV,IAAA,CAACtF,MAAM,EACLiL,OAAO,CAAEnE,QAAQ,CAAG,WAAW,CAAG,WAAY,CAC9CkH,IAAI,CAAElH,QAAQ,CAAGK,UAAU,CAAG,IAAK,CACnC+G,OAAO,CAAEvF,kBAAmB,CAC5B8G,MAAM,CAAE7I,KAAK,CAAG,OAAO,CAAG,MAAO,CACjC8I,UAAU,CAAE,CACVC,WAAW,CAAE,IACf,CAAE,CACFxF,EAAE,CAAE,CACF,oBAAoB,CAAE,CACpByF,SAAS,CAAE,YAAY,CACvB7C,KAAK,CAAEnH,WAAW,CAClBiJ,GAAG,CAAE,CAAE5B,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CAC3C4B,MAAM,CAAE,CAAE9B,EAAE,CAAE,oBAAoB,CAAEC,EAAE,CAAE,oBAAoB,CAAEC,EAAE,CAAE,oBAAqB,CAAC,CACxF0C,WAAW,CAAEjJ,KAAK,CAAG,MAAM,CAAG,+BAA+B,CAC7DkJ,UAAU,CAAElJ,KAAK,CAAG,+BAA+B,CAAG,MAAM,CAC5DmJ,SAAS,CAAE,QAAQ,CACnBjD,QAAQ,CAAE,OAAO,CACjBE,MAAM,CAAE,IAAI,CACZ,sBAAsB,CAAE,CACtBD,KAAK,CAAE,KACT,CAAC,CACD,4BAA4B,CAAE,CAC5BiD,UAAU,CAAE,SACd,CAAC,CACD,4BAA4B,CAAE,CAC5BA,UAAU,CAAE,MAAM,CAClBxF,YAAY,CAAE,KAChB,CAAC,CACD,kCAAkC,CAAE,CAClCwF,UAAU,CAAE,MACd,CACF,CACF,CAAE,CAAAhK,QAAA,CAEDkE,MAAM,CACD,CAAC,CACN,CAAC,cACN5E,IAAA,CAACxF,GAAG,EACF2L,SAAS,CAAC,MAAM,CAChBtB,EAAE,CAAE,CACFmD,QAAQ,CAAE,CAAC,CACXhD,CAAC,CAAE,CAAE2C,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC9BJ,KAAK,CAAE,MAAM,CACbkD,SAAS,CAAE,CAAEhD,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CACjDtC,SAAS,CAAE,gBAAgB/D,QAAQ,CAAG,MAAM,CAAGG,QAAQ,CAAG,MAAM,CAAG,MAAM,GAAG,CAC5EyH,QAAQ,CAAE,MACZ,CAAE,CAAA1I,QAAA,cAEFR,KAAA,CAAC7E,SAAS,EACRuP,QAAQ,CAAC,IAAI,CACb/F,EAAE,CAAE,CACFyB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3BM,EAAE,CAAE,CAAER,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAC9B,CAAE,CAAAnH,QAAA,EAEDA,QAAQ,CAGZ,CAAAO,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,IAAK,SAAS,eAC9BzC,IAAA,CAACH,qBAAqB,EACnB6I,IAAI,CAAE/H,iBAAiB,EAAIE,YAAY,EAAInB,KAAK,CAACC,GAAG,CAAC,CAAC,CAACkL,OAAO,CAACnL,KAAK,CAACC,GAAG,CAACkB,YAAY,CAACiK,QAAQ,CAAC,CAACC,GAAG,CAAClK,YAAY,CAACmK,QAAQ,EAAI,EAAE,CAAE,QAAQ,CAAC,CAAE,CAC5IC,OAAO,CAAEpK,YAAa,CACtBqK,QAAQ,CAAE,CAAAjK,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEiK,QAAQ,GAAI,IAAK,CACxCC,QAAQ,CAAEnE,0BAA2B,CACrC4B,OAAO,CAAE,IAAK,0BAA2B,CAC1C,CACH,EACY,CAAC,CACT,CAAC,cACN1I,KAAA,CAACpE,MAAM,EACL4M,IAAI,CAAEvG,gBAAiB,CACvByG,OAAO,CAAEjE,kBAAmB,CAC5B,kBAAgB,qBAAqB,CACrC,mBAAiB,2BAA2B,CAAAjE,QAAA,eAE5CV,IAAA,CAAC9D,WAAW,EAACkP,EAAE,CAAC,qBAAqB,CAAA1K,QAAA,CAClCK,CAAC,CAAC,yBAAyB,CAAC,CAClB,CAAC,cACdf,IAAA,CAAChE,aAAa,EAAA0E,QAAA,cACZV,IAAA,CAAC/D,iBAAiB,EAACmP,EAAE,CAAC,2BAA2B,CAAA1K,QAAA,CAC9CK,CAAC,CAAC,2BAA2B,CAAC,CACd,CAAC,CACP,CAAC,cAChBb,KAAA,CAACnE,aAAa,EAAA2E,QAAA,eACZV,IAAA,CAACpE,MAAM,EAACwK,OAAO,CAAEzB,kBAAmB,CAACa,KAAK,CAAC,SAAS,CAAA9E,QAAA,CACjDK,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,cACTf,IAAA,CAACpE,MAAM,EAACwK,OAAO,CAAE1B,mBAAoB,CAACc,KAAK,CAAC,SAAS,CAAC6F,SAAS,MAAA3K,QAAA,CAC5DK,CAAC,CAAC,gBAAgB,CAAC,CACd,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}